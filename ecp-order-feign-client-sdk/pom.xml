<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.jlr.ecp</groupId>
        <artifactId>ecp-order-service</artifactId>
          <version>1.1.77-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ecp-order-feign-client-sdk</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        Subscription 模块 API，暴露给其它模块调用
    </description>

    <dependencies>
        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jlr.ecp</groupId>
            <artifactId>ecp-framework-starter-rpc</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
            <version>3.3.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

    </dependencies>
    <repositories>
        <repository>
            <id>jlr-ecp-nexus</id>
            <name>jlr-ecp-nexus</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>jlr-ecp-release</id>
            <name>Releases</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-releases/</url>
        </repository>
        <snapshotRepository>
            <id>jlr-ecp-snapshot</id>
            <name>Snapshot</name>
            <url>https://nexus.jaguarlandrover.cn/repository/ecp-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
