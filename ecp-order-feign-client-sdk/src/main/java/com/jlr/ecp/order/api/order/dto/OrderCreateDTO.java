package com.jlr.ecp.order.api.order.dto;

import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import com.jlr.ecp.order.api.order.dto.customer.GuestOrderCreateCustomerServiceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 前端传入的订单创建DTO
 *
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 前端传入的订单创建DTO")
@Data
public class OrderCreateDTO {
    /**
     * 0.一些全局信息
     * 品牌编码
     * 渠道编码
     */
    @Valid
    @NotNull(message = "一些全局信息不能为空")
    GlobalInfoDTO globalInfoDTO;

    /**
     * 0.购物车item基本信息
     */
    @Valid
    @NotNull(message = "购物车item基本信息不能为空")
    List<OrderShopCarItemDTO> shopCarItemList;

    /**
     * 订单信息 包括
     * 1.登录信息 IN CONTROL ID
     * 2.联系人信息
     * 3.支付信息
     * 4.条款信息
     * <p>
     * //TODO 查询接口 2.进入订单 通过购物车item的product_code 在 t_product_relation_info 查distinct policy list
     */
    @Valid
    @NotNull(message = "订单信息不能为空")
    OrderInfoDTO orderInfoDTO;

    @Valid
    @NotNull(message = "赠品信息不能为空")
    OrderGiftAddressDTO giftInfoDTO;

    @Schema(description = "优惠券编码")
    String couponCode;

    @Schema(description = "现金+积分时，对应的sku编码列表")
    List<String> productSkuCodeList;

    @Schema(description = "1:现金 ，2：积分，3:优惠券")
    private String paymentType;

    @Schema(description = "是否需要删除ICR校验的redisKey")
    boolean delKey = false;


    /**
     * 代客下单信息
     *
     * 接收付款短信手机号 不为空
     * 短信模板编码 不为空
     * 订单备注 不超过50位
     */
    @Valid
    GuestOrderCreateCustomerServiceDTO customerServiceOrderDTO;

    public void validateContactPhone() {
        if (StrUtil.isBlank(this.orderInfoDTO.getContactInfoDTO().getContactPhone())) {
            throw new IllegalArgumentException("手机号不能为空");
        }
    }
}
