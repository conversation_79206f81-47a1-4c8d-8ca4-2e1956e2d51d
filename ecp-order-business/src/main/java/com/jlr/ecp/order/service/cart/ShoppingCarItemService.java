package com.jlr.ecp.order.service.cart;


import com.jlr.ecp.order.controller.app.cart.dto.CartGroupedBylineReq;
import com.jlr.ecp.order.controller.app.cart.vo.ShopCartMultiTypeProdVo;
import com.jlr.ecp.order.controller.app.cart.vo.ShoppingCarItemVO;

import java.util.List;

/**
 * 购物车主表表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-18
 */
public interface ShoppingCarItemService {


    /**
     * 查询购物车列表
     * @param consumerCode 用户编码
     * @return  List<ShoppingCarItemListVO>
     */
    List<ShoppingCarItemVO> findCartList(String consumerCode, String brandCode, String incontrolName);

    /**
     * 统计购物车商品数量
     *
     * @param consumerCode  用户 JLR ID
     * @param incontrolName 用户 ICR 账号
     * @return 用户购物车商品数量
     */
    Integer countCartNum(String consumerCode, String incontrolName);

    /**
     * 删除购物车商品
     * @param cartItemCodes 购物车栏目
     * @return String
     */
    Boolean delete(List<String> cartItemCodes);

    /**
     * 登出时清空购物车商品
     * @param consumerCode 用户编码
     * @return String
     */
    Boolean deleteByConsumerCode(String consumerCode);

    /**
     * 登出时清空购物车商品
     * @param consumerCode 用户编码
     * @param incontrolId incontrolId账号
     * @return String
     */
    Boolean deleteByConsumerCodeAndICR(String consumerCode, String incontrolId, String brandCode);

    /**
     * 查询购物车列表 (根据业务线分组)
     *
     * @param jlrId                JLR ID
     * @param cartGroupedBylineReq ICR 账户信息
     * @return ShopCartMultiTypeProdVo
     */
    ShopCartMultiTypeProdVo productByServiceLineList(String jlrId, CartGroupedBylineReq cartGroupedBylineReq);
}

