package com.jlr.ecp.order.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.jlr.ecp.consumer.api.consumer.ConsumerApi;
import com.jlr.ecp.consumer.api.consumer.dto.ConsumerInfoDTO;
import com.jlr.ecp.framework.common.exception.ErrorCode;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.common.util.json.JsonUtils;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.security.core.service.SecurityFrameworkService;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.notification.api.dto.ShortLinkReqDto;
import com.jlr.ecp.notification.api.jaguarlandover.ShorLinkAPI;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import com.jlr.ecp.order.api.order.dto.customer.BaseOrderShopCarItemDTO;
import com.jlr.ecp.order.api.order.dto.customer.ResendMessageDTO;
import com.jlr.ecp.order.api.order.vo.*;
import com.jlr.ecp.order.api.order.vo.address.AppOrderGiftAddressDetailVO;
import com.jlr.ecp.order.api.order.vo.address.OrderGiftAddressDetailVO;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPage;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPolicyRespVO;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.*;
import com.jlr.ecp.order.api.order.vo.coupon.ECouponOrderDetailVO;
import com.jlr.ecp.order.api.order.vo.detail.*;
import com.jlr.ecp.order.api.order.vo.feedback.OrderFeedbackInfo;
import com.jlr.ecp.order.api.order.vo.orderlist.OrderSkuItemVo;
import com.jlr.ecp.order.api.payment.dto.PayRequestDTO;
import com.jlr.ecp.order.component.SubmitPayOrderComponent;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.controller.app.order.dto.OrderLatestListReqDTO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.OrderIntegrationLatestOrderRespVO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.OrderIntegrationPageRespVO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo.BgGood;
import com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo.BgInfo;
import com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo.OrderIntegrationRespVO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo.VcsInfo;
import com.jlr.ecp.order.controller.app.order.vo.PayOrderRespVO;
import com.jlr.ecp.order.dal.dataobject.customer.order.CustomerServiceOrderDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackConfigDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackRecordsDO;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.customer.order.CustomerServiceOrderDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackConfigDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackRecordsDOMapper;
import com.jlr.ecp.order.dal.mysql.order.*;
import com.jlr.ecp.order.dal.mysql.order.po.OrderItemRefundVcsPo;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CartItemTypeEnum;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.enums.customer.BindCustomerEnum;
import com.jlr.ecp.order.enums.feedback.FeedBackEnableStatusEnum;
import com.jlr.ecp.order.enums.feedback.FeedBackTypeEnum;
import com.jlr.ecp.order.enums.independent.OrderIndependentStatusEnum;
import com.jlr.ecp.order.enums.order.*;
import com.jlr.ecp.order.enums.payment.*;
import com.jlr.ecp.order.enums.phone.PhoneNumberDecodeUtil;
import com.jlr.ecp.order.enums.sms.ShortLinkPathEnum;
import com.jlr.ecp.order.handle.RefundHandler;
import com.jlr.ecp.order.kafka.FulfilmentMessage;
import com.jlr.ecp.order.kafka.OrderECouponSuccessfulMessage;
import com.jlr.ecp.order.kafka.OrderPaymentSuccessMessage;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import com.jlr.ecp.order.kafka.message.customer.ValetOrderMessage;
import com.jlr.ecp.order.kafka.producer.BrandedGoodsNotificationProducer;
import com.jlr.ecp.order.service.cart.ShoppingCarItemService;
import com.jlr.ecp.order.service.order.address.OrderGiftAddressService;
import com.jlr.ecp.order.service.order.customer.order.CustomerServiceOrderDOService;
import com.jlr.ecp.order.service.order.handler.ShortLinkHandler;
import com.jlr.ecp.order.util.OrderTitleUtil;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.order.util.common.AttributeUtil;
import com.jlr.ecp.order.util.common.TimeFormatUtil;
import com.jlr.ecp.order.util.feedback.FeedbackParserUtil;
import com.jlr.ecp.order.util.machine.OrderRefundStatusMachine;
import com.jlr.ecp.order.util.money.MoneyUtil;
import com.jlr.ecp.order.util.order.OrderCodeGenerator;
import com.jlr.ecp.order.util.order.RequestValidationUtil;
import com.jlr.ecp.order.util.order.SequenceNumberGenerator;
import com.jlr.ecp.payment.api.invoice.InvoiceApiV2;
import com.jlr.ecp.payment.api.invoice.dto.ESpecialInvoiceModifyReqDTO;
import com.jlr.ecp.payment.api.invoice.dto.ESpecialInvoiceModifyRespDTO;
import com.jlr.ecp.payment.api.invoice.dto.PaperInvoiceModifyReqDTO;
import com.jlr.ecp.payment.api.invoice.dto.PaperInvoiceModifyRespDTO;
import com.jlr.ecp.payment.api.invoice.vo.InvoiceDetailVO;
import com.jlr.ecp.payment.api.invoice.vo.InvoiceStatusVO;
import com.jlr.ecp.payment.api.order.PayCenterOrderApi;
import com.jlr.ecp.payment.api.order.vo.SubmitPayOrderReq;
import com.jlr.ecp.payment.api.order.vo.SubmitPayOrderResp;
import com.jlr.ecp.payment.enums.order.PayOrderStatusEnum;
import com.jlr.ecp.product.api.policy.PolicyApi;
import com.jlr.ecp.product.api.policy.vo.OrderDetailPolicyRespVO;
import com.jlr.ecp.product.api.sku.ProductSkuApi;
import com.jlr.ecp.product.api.sku.dto.AllPaymentInfoDTO;
import com.jlr.ecp.product.api.sku.dto.CartItemDTO;
import com.jlr.ecp.product.api.sku.dto.OrderValidationInfo;
import com.jlr.ecp.product.api.sku.dto.ProductSnapshotDTO;
import com.jlr.ecp.product.api.snapshot.ProductSnapshotApi;
import com.jlr.ecp.product.enums.product.ProductFulfilmentTypeEnum;
import com.jlr.ecp.product.enums.product.SortOrder;
import com.jlr.ecp.subscription.api.consumer.ConsumerServiceApi;
import com.jlr.ecp.subscription.api.consumer.dto.ConsumerIncontrolDTO;
import com.jlr.ecp.subscription.api.icrvehicle.IcrVehicleApi;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.subscription.api.incontrol.IncontrolVehicleAPI;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.manual.ManualRenewServiceApi;
import com.jlr.ecp.subscription.api.manual.dto.CheckRecordsInTransitDTO;
import com.jlr.ecp.subscription.api.subscripiton.SubscriptionServiceApi;
import com.jlr.ecp.subscription.api.subscripiton.dto.SubscriptionServiceQueryDTO;
import com.jlr.ecp.subscription.api.subscripiton.vo.SubscriptionServiceQueryVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.VcsOrderFulfilmentApi;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.system.api.permission.PermissionApi;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.jlr.ecp.order.constant.Constants.*;
import static com.jlr.ecp.order.enums.ErrorCodeConstants.*;
import static com.jlr.ecp.order.enums.order.OrderChannelCodeEnum.*;
import static com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum.*;
import static com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum.REFUND_COMPLETED;
import static com.jlr.ecp.order.enums.sms.ShortLinkPathEnum.BEHALFOF_ORDER;
import static com.jlr.ecp.order.enums.sms.ShortLinkPathEnum.JAGUAR_BEHALFOF_ORDER;

/**
 * <AUTHOR>
 * @description 针对表【t_order_info(t_order_info)】的数据库操作Service实现
 * @createDate 2023-12-20 10:41:04
 */
@Service
@Slf4j
public class OrderInfoDOServiceImpl extends ServiceImpl<OrderInfoDOMapper, OrderInfoDO> implements OrderInfoDOService {

    @Resource
    private ProductSkuApi productSkuApi;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    Snowflake ecpIdUtil;
    @Resource
    private PIPLDataUtil piplDataUtil;
    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;
    @Resource
    private OrderItemDOService orderItemDOService;
    @Resource
    private OrderStatusLogDOMapper orderStatusLogDOMapper;
    @Resource
    private OrderStatusLogDOService orderStatusLogDOService;
    @Resource
    private OrderTermsDOService orderTermsDOService;
    @Resource
    private IncontrolVehicleAPI incontrolVehicleAPI;
    @Resource
    private VcsOrderInfoDOService vcsOrderInfoDOService;
    @Resource
    private VcsOrderInfoDOMapper vcsOrderInfoDOMapper;
    @Resource
    private ShoppingCarItemService shoppingCarItemService;
    @Resource
    private OrderItemDOMapper orderItemDOMapper;
    @Resource
    private OrderModifyDetailLogDOMapper modifyDetailLogMapper;
    @Resource
    private VcsOrderFulfilmentApi vcsOrderFulfilmentApi;
    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;
    @Resource
    private OrderStatusMappingDOMapper orderStatusMappingDOMapper;
    @Resource
    private OrderTermsDOMapper orderTermsDOMapper;
    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;
    @Resource
    private PolicyApi policyApi;
    @Resource
    private ProducerTool producerTool;
    @Resource
    private VcsOrderStatisticDOMapper vcsOrderStatisticDOMapper;
    @Resource
    private OrderStatisticDOMapper orderStatisticDOMapper;
    @Resource
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;
    @Resource
    private OrderGiftAddressMapper orderGiftAddressMapper;
    @Resource
    private ConsumerApi consumerApi;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private OrderRefundStatusMachine orderRefundStatusMachine;
    @Resource
    private SubscriptionServiceApi subscriptionServiceApi;
    @Resource
    private ProductSnapshotApi productSnapshotApi;
    @Resource
    private ShortLinkHandler shortLinkHandler;
    @Resource
    private BrandedGoodsNotificationProducer notificationProducer;

    @Value("${order-successful-code:order-successful-code}")
    private String payment;

    @Value("${taskCode.order.cancel:order-cancel-code}")
    private String cancel;

    @Resource
    private ShorLinkAPI shorLinkAPI;
    @Resource
    private InvoiceApiV2 invoiceApi;
    @Resource
    private PayCenterOrderApi payOrderApi;
    @Resource
    private PhoneNumberDecodeUtil phoneNumberDecodeUtil;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private final static Long ADMIN_USER_ID = 1L;



    @Resource
    private RedisService redisService;
    @Resource
    private ConsumerServiceApi consumerServiceApi;
    @Resource
    private IcrVehicleApi icrVehicleApi;
    @Resource
    private SecurityFrameworkService securityFrameworkService;
    @Resource
    private OrderGiftAddressService orderGiftAddressService;

    private static final String STATUS_TXT = " (拒绝退款)";

    @Resource
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;
    @Resource
    private CustomerServiceOrderDOService customerServiceOrderDOService;
    @Resource
    private CustomerServiceOrderDOMapper customerServiceOrderDOMapper;
    @Resource
    private FeedbackRecordsDOMapper feedbackRecordsDOMapper;
    @Resource
    private ManualRenewServiceApi manualRenewServiceApi;
    @Resource
    private FeedbackConfigDOMapper feedbackConfigDOMapper;
    @Resource
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;
    @Resource
    private OrderDiscountDetailDOMapper orderDiscountDetailDOMapper;
    @Resource
    private SubmitPayOrderComponent submitPayOrderComponent;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private RefundHandler refundHandler;

    private static final String VALET_ORDER_LIST_PERMISSION = "trade:on-behalf-of-order:list";

    private static final String ORDER_PAGE_LIST_PERMISSION = "trade:order:list";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCreateRespVO createOrderInfo(OrderCreateDTO orderCreateDTO, String clientId) {
        log.info("service层创建订单，orderCreateDTO:{}", JSON.toJSONString(orderCreateDTO));

        // 原有下单接口前置校验
        OrderCreateRespVO checkResult = performPreOrderValidations(orderCreateDTO, clientId);
        if (checkResult != null) {
            return checkResult;
        }

        //2. 数据准备
        String oneID = orderCreateDTO.getGlobalInfoDTO().getConsumerCode(); // 现在oneID 是作为consumerCode进行传入
        boolean needSplit = determineIfSplitNeeded(orderCreateDTO); //检测是否需要生成父订单
        LocalDateTime now = LocalDateTime.now(); //当前时间
        OrderCreateRespVO response; //构造响应对象

        if (!needSplit) {           //2.1 不拆单：当所有商品项属于同一业务线，同一车辆，同一履约类型时调用此方法
            response = processSingleFulfillmentOrder(orderCreateDTO, oneID, now, clientId);
        } else {                    //2.2 拆单、查信息、组装
            // 2.2.1 生成父订单
            OrderInfoDO parentOrder = createParentOrder(orderCreateDTO, oneID, now);
            String parentOrderCode = parentOrder.getOrderCode();

            // 2.2.2 处理不同业务线的拆单逻辑
            response = processBusinessSplit(orderCreateDTO, parentOrderCode, oneID, now, clientId);

            // 2.2.3 保存数据
            orderInfoDOMapper.insert(parentOrder);
        }
        //异步清除订单提交内的购物车商品
        asyncClearShoppingCartItems(orderCreateDTO);
        //TODO 异步更新 购买数量、库存

        return response;
    }

    /**
     * 原有下单接口前置校验
     * <p>
     * - globalBrandCode、channelCode需一一对应      eg.LR对应MLR; 代客下单时，globalBrandCode为LR or JA or JLR，channelCode为CS
     * - 购物车itemList.size、单个item.quantity 都需>0
     * - 通过ConsumerCode、clientId去sub服务查看此时'登陆绑定的icr'和'下单的icr'是否一致   eg.代客下单为常量值CUSTOMER_SERVICE_ORDER跳过校验
     * - 抽取商品、金额相关参数，在product服务进行校验，返回 skuCode和对应的信息包括一个商品对应的子(组合商品）的信息
     * <p>
     * - 检查是否存在未支付或售后处理中的商品订单
     * - 同一辆vin不能重复下单的情况  正在激活中 激活关闭中的服务不可购买
     * <p>
     * - 为代客下单时，调用 代客下单页面商品选购 校验接口
     */
    @Nullable
    private OrderCreateRespVO performPreOrderValidations(OrderCreateDTO orderCreateDTO, String clientId) {

        // - 在处理订单之前先验证全局信息中的渠道编码
        if (!orderCreateDTO.getGlobalInfoDTO().validate()) {
            throw exception(CHANNEL_CODE_ERROR);
        }

        // - 购物车参数校验
        validate(orderCreateDTO.getShopCarItemList());

        // - 收货信息参数校验
        if (EnumUtil.getBy(GiftAddressEnum::getCode, orderCreateDTO.getGiftInfoDTO().getNeedGift()) == null) {
            throw exception(GIFT_ADDRESS_INVALID);
        }
        validate(orderCreateDTO.getGiftInfoDTO());

        // - check consumer和 ICR
        String currentConsumerCode = orderCreateDTO.getGlobalInfoDTO().getConsumerCode();
        OrderCreateRespVO checkConsumerAndICRResult = checkConsumerAndICR(currentConsumerCode, orderCreateDTO, clientId);
        if (checkConsumerAndICRResult != null) {
            return checkConsumerAndICRResult;
        }

        // - 抽取商品、金额相关参数，在product服务进行校验，返回 skuCode和对应的信息包括一个商品对应的子(组合商品）的信息
        //a.从前端传入的参数中提取校验所需的信息
        OrderValidationInfo orderValidationInfo = extractOrderValidationInfo(orderCreateDTO);
        log.info("extractOrderValidationInfo,orderValidationInfo:{}", JSON.toJSONString(orderValidationInfo));
        //b.调用远程服务
        CommonResult commonResult = productSkuApi.verifyOrderProducts(orderValidationInfo);
        if (commonResult.getCode() != 0) {
            throw exception(new ErrorCode(commonResult.getCode(), commonResult.getMsg()));
        }
        //c.取出最新版本的商品信息 key为sku
        Map<String, ProductSnapshotDTO> latestSnapshotMap = new HashMap<>();
        Map<String, LinkedHashMap> rawDataMap = (Map<String, LinkedHashMap>) commonResult.getData();
        for (Map.Entry<String, LinkedHashMap> entry : rawDataMap.entrySet()) {
            ProductSnapshotDTO snapshot = objectMapper.convertValue(entry.getValue(), ProductSnapshotDTO.class);
            latestSnapshotMap.put(entry.getKey(), snapshot);
        }
        log.info("最新版本的商品map，latestSnapshotMap:{}", JSON.toJSONString(latestSnapshotMap));

        // carVin::serviceType作为唯一key
        Set<String> carVinAndServiceTypeSet = new HashSet<>();
        Set<String> carVinMd5AndServiceTypeSet = new HashSet<>();
        // 遍历 shopCarItemList 并更新其属性
        boolean countLimit = buildShopCarItemList(orderCreateDTO, latestSnapshotMap, carVinAndServiceTypeSet, carVinMd5AndServiceTypeSet);

        // 只能下单VCS商品
        validateBusiness(orderCreateDTO);

        // - 校验单个车是否有服务类型重复的情况
        if (countLimit) {
            log.info("下单时商品服务类型重复, shopCarItemList={}", JSON.toJSONString(orderCreateDTO.getShopCarItemList()));
            throw exception(ORDER_ITEM_REPEAT);
        }

        if (CollUtil.isNotEmpty(carVinAndServiceTypeSet)) {
            // - 检查是否存在未支付或售后处理中的商品订单
            OrderCreateRespVO checkResult = checkUnpaidOrAfterSalesVcsOrders(orderCreateDTO, carVinMd5AndServiceTypeSet);
            if (checkResult != null) {
                return checkResult;
            }

            // - 同一辆vin不能重复下单的情况  2.正在激活中 激活关闭中的服务不可购买
            OrderCreateRespVO constraintCheckResponse = checkForExistingVcsOrderConstraints(carVinAndServiceTypeSet);
            if (constraintCheckResponse != null) {
                return constraintCheckResponse;
            }

            // sprint47: 校验是否存在在途的手动续费订单
            OrderCreateRespVO checkManualRenewInTransit = checkManualRenewInTransit(carVinAndServiceTypeSet);
            if (checkManualRenewInTransit != null) {
                return checkManualRenewInTransit;
            }
        }

        //为代客下单时，调用 代客下单页面商品选购 校验接口
        validateCustomerServiceOrderProducts(currentConsumerCode, carVinAndServiceTypeSet);
        return null;
    }

    /**
     * 为代客下单时，调用 代客下单页面-商品选购 校验接口
     * @param currentConsumerCode
     * @param carVinAndServiceTypeSet
     */
    private void validateCustomerServiceOrderProducts(String currentConsumerCode, Set<String> carVinAndServiceTypeSet) {
        boolean isCustomerServiceOrder = CUSTOMER_SERVICE_ORDER.equals(currentConsumerCode);
        log.info("为代客下单时，调用 代客下单页面商品选购 校验接口, currentConsumerCode:{}", currentConsumerCode);

        if (isCustomerServiceOrder) {
            List<com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO> dtoList = carVinAndServiceTypeSet.stream().map(s -> {
                com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO dto = new com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO();
                String[] split = s.split(CONCAT_SYMBOL);
                dto.setCarVin(split[0]);
                dto.setServiceType(Integer.parseInt(split[1]));
                return dto;
            }).collect(Collectors.toList());
            log.info("调用checkCanBuyServiceForPC前，传入参数dtoList:{}", JSON.toJSONString(dtoList));

            CommonResult<Boolean> booleanCommonResult = vcsOrderFulfilmentApi.checkCanBuyServiceForPC(dtoList);
            log.info("调用checkCanBuyServiceForPC, 返回结果booleanCommonResult:{}", JSON.toJSONString(booleanCommonResult));
            if (booleanCommonResult.getCode() != 0) {
                throw exception(new ErrorCode(booleanCommonResult.getCode(), booleanCommonResult.getMsg()));
            }
        }
    }

    /**
     * 根据购物车项和最新的商品快照，构建购物车商品项列表。
     *
     * @param orderCreateDTO             订单创建DTO，包含购物车商品项列表。
     * @param latestSnapshotMap          最新的商品快照映射表，用于查找商品快照。
     * @param carVinAndServiceTypeSet    用于存储车辆VIN和商品类型的组合，用于后续处理。
     * @param carVinMd5AndServiceTypeSet 用于存储车辆VIN的MD5和商品类型的组合，用于后续处理。
     */
    private static boolean buildShopCarItemList(OrderCreateDTO orderCreateDTO, Map<String, ProductSnapshotDTO> latestSnapshotMap, Set<String> carVinAndServiceTypeSet, Set<String> carVinMd5AndServiceTypeSet) {
        Map<String, Integer> vinCountMap = new HashMap<>();
        for (OrderShopCarItemDTO item : orderCreateDTO.getShopCarItemList()) {
            String productSkuCode = item.getProductSkuCode();
            log.info("Processing shop cart item with SKU code: {}", productSkuCode);

            ProductSnapshotDTO snapshot = latestSnapshotMap.get(item.getProductSkuCode());
            if (snapshot != null) {
                log.info("Found latest snapshot for SKU code: {}", productSkuCode);

                // *如果是捆绑商品, 新增组合商品item
                List<OrderShopCarItemDTO> childList = addChildShopCarItem(item, snapshot);
                log.info("Added child shop cart items: {}", JSON.toJSONString(childList));

                // 更新 OrderShopCarItemDTO 的属性
                BeanUtil.copyProperties(snapshot, item, "productAttribute");
                item.setSalePrice(MoneyUtil.convertFromCents(new BigDecimal(snapshot.getProductSalePrice())));
                if (snapshot.getProductMarketPrice() != null) {
                    item.setMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(snapshot.getProductMarketPrice())));
                }
                item.setChildList(childList);
            }
            // 实物商品不做处理
            if (CartItemTypeEnum.BRAND_GOODS.getCode().equals(item.getCartItemType())) {
                continue;
            }
            String carVin = item.getCarVin();
            String carVinMd5 = SecureUtil.md5(carVin);
            // 组合商品要查看子商品的商品类型
            if (CartItemTypeEnum.BUNDLED_GOODS.getCode().equals(item.getCartItemType())) {
                for (OrderShopCarItemDTO child : item.getChildList()) {
                    String key = carVin + CONCAT_SYMBOL + child.getCartItemType();
                    carVinAndServiceTypeSet.add(carVin + CONCAT_SYMBOL + child.getCartItemType());
                    carVinMd5AndServiceTypeSet.add(carVinMd5 + CONCAT_SYMBOL + child.getCartItemType());
                    // 如果不存在返回0
                    Integer count = vinCountMap.getOrDefault(key, 0);
                    vinCountMap.put(key, count + 1);
                }
            } else {
                String key = carVin + CONCAT_SYMBOL + item.getCartItemType();
                carVinAndServiceTypeSet.add(key);
                carVinMd5AndServiceTypeSet.add(carVinMd5 + CONCAT_SYMBOL + item.getCartItemType());
                // 如果不存在返回0
                Integer count = vinCountMap.getOrDefault(key, 0);
                vinCountMap.put(key, count + 1);
            }
        }
        return vinCountMap.values().stream().anyMatch(count -> count > 1);
    }

    /**
     * 新增组合商品下的item，并计算每个子项的payPrice。
     */
    private static List<OrderShopCarItemDTO> addChildShopCarItem(OrderShopCarItemDTO parentItem, ProductSnapshotDTO snapshot) {
        log.info("新增组合商品下的parentItem:{}", JSON.toJSONString(parentItem));

        // 组合商品下子商品list
        List<ProductSnapshotDTO> childProductSnapshotList = snapshot.getChildProductSnapshotList();
        if (CollUtil.isEmpty(childProductSnapshotList)) {
            log.info("组合商品下没有子商品，parentItem:{}", JSON.toJSONString(parentItem));
            return Collections.emptyList();
        }

        // 计算组合商品下 SUM(item.salePrice * item.productQuantity)
        BigDecimal totalSalePrice = childProductSnapshotList.stream()
                .filter(Objects::nonNull)
                .map(dto -> new BigDecimal(dto.getProductSalePrice()).multiply(BigDecimal.valueOf(dto.getProductQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("组合商品下 SUM(item.salePrice * item.productQuantity) = {}", totalSalePrice);

        // 前端传过来的payPrice转换成分 eg.前端传入700.00 转换成 70000
        BigDecimal parentPayPrice = MoneyUtil.convertToCents(parentItem.getPayPrice());
        log.info("前端传过来的payPrice转换成分 = {}", parentPayPrice);

        BigDecimal remainingPayPrice = parentPayPrice;
        List<OrderShopCarItemDTO> calculatedChildItems = new ArrayList<>();

        // 如果是捆绑商品, 新增组合商品item
        for (int i = 0; i < childProductSnapshotList.size(); i++) {
            ProductSnapshotDTO dto = childProductSnapshotList.get(i);
            if (dto == null) {
                continue;
            }
            OrderShopCarItemDTO childItem = new OrderShopCarItemDTO();
            BeanUtil.copyProperties(parentItem, childItem, "cartItemType", "marketPrice");
            childItem.setQuantity(parentItem.getQuantity() * dto.getProductQuantity());
            BeanUtil.copyProperties(dto, childItem);
            childItem.setCartItemType(dto.getFulfilmentType());

            // 子商品的单价*子商品的数量
            BigDecimal salePrice = new BigDecimal(dto.getProductSalePrice()).multiply(BigDecimal.valueOf(dto.getProductQuantity()));
            log.info("子商品的单价*子商品的数量 = {}", salePrice);

            if (i < childProductSnapshotList.size() - 1) {
                // 按照销售价格的比例分配实付价格
                BigDecimal ratio = salePrice.divide(totalSalePrice, 8, RoundingMode.HALF_UP);
                log.info("按照销售价格的比例 = {}", ratio);
                BigDecimal payPrice = parentPayPrice.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                log.info("按照销售价格的比例分配实付价格 = {}", payPrice);
                childItem.setPayPrice(MoneyUtil.convertFromCents(payPrice)); // 将实付价格转换为分 字符串 700.00
                remainingPayPrice = remainingPayPrice.subtract(payPrice);
                log.info("执行第{} 次,剩余的实付价格 = {}", i + 1, remainingPayPrice);
            } else {
                // 最后一个子项，使用剩余的实付价格
                childItem.setPayPrice(MoneyUtil.convertFromCents(remainingPayPrice));
                log.info("最后一个子项，使用剩余的实付价格 = {}", remainingPayPrice);
            }

            childItem.setSalePrice(MoneyUtil.convertFromCents(new BigDecimal(dto.getProductSalePrice())));
            if (dto.getProductMarketPrice() != null) {
                childItem.setMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(dto.getProductMarketPrice())));
            }
            calculatedChildItems.add(childItem);
        }
        log.info("新增组合商品下的childItem:{}", JSON.toJSONString(calculatedChildItems));
        return calculatedChildItems;
    }

    /**
     * 代客下单时，为常量值CUSTOMER_SERVICE_ORDER，跳过检验
     *
     * @param currentConsumerCode
     * @param orderCreateDTO
     * @param clientId
     */
    private OrderCreateRespVO checkConsumerAndICR(String currentConsumerCode, OrderCreateDTO orderCreateDTO, String clientId) {
        // 代客下单直接跳过校验
        if (CUSTOMER_SERVICE_ORDER.equals(currentConsumerCode)) {
            return null;
        }

        OrderCreateRespVO orderCreateRespVO = validateICRByRedis(currentConsumerCode, orderCreateDTO);
        if (orderCreateRespVO != null) return orderCreateRespVO;

        CommonResult<ConsumerIncontrolDTO> consumerByCodeOrICR = consumerServiceApi.getConsumerByCodeOrICR(currentConsumerCode, null, clientId);
        if (consumerByCodeOrICR != null && consumerByCodeOrICR.isSuccess()) {
            ConsumerIncontrolDTO data = consumerByCodeOrICR.getData();
            String incontrolId = orderCreateDTO.getOrderInfoDTO().getIncontrolId();
            if (data == null || !incontrolId.equals(data.getIncontrolId())) {
                throw exception(CHECK_ICR_CONSUMER_ERROR);
            }
        } else {
            log.info("checkConsumerAndICR api error!");
        }
        return null;
    }

    /**
     * 根据Redis缓存校验ICR（In-Control Relationship）关系状态，判断当前消费者在创建订单时是否存在正在验证、未绑定或换绑的ICR关系。
     *
     * @param currentConsumerCode 当前消费者的编码
     * @param orderCreateDTO      订单创建请求数据传输对象，包含购物车中的车辆信息
     * @return 如果存在正在验证的ICR关系，则返回标记为正在验证的响应对象；
     *         如果存在验证失败的ICR关系（未绑定或换绑），则返回对应标记及VIN列表的响应对象；
     *         如果均无异常ICR关系，则返回null。
     */
    private OrderCreateRespVO validateICRByRedis(String currentConsumerCode, OrderCreateDTO orderCreateDTO) {
        Set<String> vinSet = orderCreateDTO.getShopCarItemList()
                .stream()
                .map(BaseOrderShopCarItemDTO::getCarVin)
                .collect(Collectors.toSet());

        // 删除redisKey,跳过后续校验
        if (orderCreateDTO.isDelKey()) {
            List<String> validCacheKeys = vinSet.stream()
                    .map(vin -> REDIS_KEY.ICR_VALID_CACHE_KEY + currentConsumerCode + ":" + vin)
                    .collect(Collectors.toList());
            redisTemplate.delete(validCacheKeys);
            return null;
        }

        OrderCreateRespVO orderCreateRespVO = new OrderCreateRespVO();
        // 校验是否有正在验证的ICR关系
        for (String carVin : vinSet) {
            String checkCacheKey = REDIS_KEY.ICR_CHECK_CACHE_KEY + currentConsumerCode + ":" + carVin;
            Object checkCacheValue = redisTemplate.opsForValue().get(checkCacheKey);
            // check缓存不为空，则直接返回
            if (Objects.nonNull(checkCacheValue)) {
                orderCreateRespVO.setHasICRValidating(true);
                return orderCreateRespVO;
            }
        }

        boolean hasUnbindICR = false;
        boolean hasChangedICR = false;
        List<String> unbindICRCarVinList = new ArrayList<>();
        List<String> changedICRCarVinList = new ArrayList<>();
        // 查询是否有验证失败的ICR关系
        for (String carVin : vinSet) {
            String validCacheKey = REDIS_KEY.ICR_VALID_CACHE_KEY + currentConsumerCode + ":" + carVin;
            Object validCacheValue = redisTemplate.opsForValue().get(validCacheKey);
            if (Objects.isNull(validCacheValue)) {
                continue;
            }
            // valid缓存不为空，将vin加入返回列表.优先级未绑定>换绑
            if (InControlIdBindStatusEnum.CHANGE_BIND.getStatus().equals(validCacheValue.toString())) {
                hasChangedICR = true;
                changedICRCarVinList.add(carVin);
            } else {
                hasUnbindICR = true;
                unbindICRCarVinList.add(carVin);
            }
        }

        if (hasUnbindICR) {
            orderCreateRespVO.setHasUnbindICR(true);
            orderCreateRespVO.setCarVinList(unbindICRCarVinList);
            return orderCreateRespVO;
        }
        if (hasChangedICR) {
            orderCreateRespVO.setHasChangedICR(true);
            orderCreateRespVO.setCarVinList(changedICRCarVinList);
            return orderCreateRespVO;
        }
        return null;
    }

    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     * 2.正在激活中 激活关闭中的服务不可购买
     *
     * @param carVinAndServiceTypeSet carVin::ServiceType集合
     * @return
     */

    private OrderCreateRespVO checkForExistingVcsOrderConstraints(Set<String> carVinAndServiceTypeSet) {
        log.info("carVinAndServiceTypeSet:{}", carVinAndServiceTypeSet);
        List<com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO> dtoList = carVinAndServiceTypeSet.stream().map(s -> {
            com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO dto = new com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO();
            String[] split = s.split(CONCAT_SYMBOL);
            dto.setCarVin(split[0]);
            dto.setServiceType(Integer.parseInt(split[1]));
            return dto;
        }).collect(Collectors.toList());
        OrderCreateRespVO orderCreateRespVO = new OrderCreateRespVO();
        try {
            CommonResult<Boolean> booleanCommonResult = vcsOrderFulfilmentApi.checkCanBuyServiceV2(dtoList);
            if (booleanCommonResult != null && booleanCommonResult.getData() != null && !booleanCommonResult.getData()) {
                orderCreateRespVO.setHasLongExpireOrActiveOrder(true);
                log.info(Constants.ORDER_CREATE_RESPONSE, JSON.toJSONString(orderCreateRespVO));
                return orderCreateRespVO;
            }
        } catch (Exception e) {
            // 可能需要更具体的异常处理策略，例如记录日志并重新抛出异常
            log.error("调用subscription checkCanBuyServiceV2异常, {}", e.getMessage());
            throw new RuntimeException("检查VCS订单约束时发生异常", e);
        }
        // 如果没有问题，则返回null，表示无需提前终止订单创建流程
        return null;
    }

    /**
     * 检查是否存在未支付或正在售后中的VCS类型商品订单
     */
    private OrderCreateRespVO checkUnpaidOrAfterSalesVcsOrders(OrderCreateDTO orderCreateDTO, Set<String> carVinMd5AndServiceTypeSet) {
        log.info("检查是否存在未支付或正在售后中的VCS类型商品订单 方法入参 orderCreateDTO:{},carVinMd5AndServiceTypeSet:{}", orderCreateDTO, carVinMd5AndServiceTypeSet);
        List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList = carVinMd5AndServiceTypeSet.stream().map(s -> {
            CarVinAndServiceTypeDTO dto = new CarVinAndServiceTypeDTO();
            String[] split = s.split(CONCAT_SYMBOL);
            dto.setCarVin(split[0]);
            dto.setServiceType(Integer.parseInt(split[1]));
            return dto;
        }).collect(Collectors.toList());
        // 得到当前下单的consumer code
        String currentConsumerCode = orderCreateDTO.getGlobalInfoDTO().getConsumerCode();

        // 获取处于未支付或售后中状态的订单信息 通过carVin 和 serviceType
        List<OrderUnpaidRelaDTO> ordersForVCS = orderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(carVinAndServiceTypeList);
        log.info("checkUnpaidOrAfterSalesVcsOrders ordersForVCS:{}", JSON.toJSONString(ordersForVCS));

        if (CollUtil.isEmpty(ordersForVCS)) {
            return null;
        }

        OrderCreateRespVO orderCreateRespVO = new OrderCreateRespVO();

        List<OrderUnpaidRelaDTO> orderUnpaidList = ordersForVCS.stream().filter(dto -> OrderStatusEnum.ORDERED.getCode().equals(dto.getOrderStatus())).collect(Collectors.toList());
        // 有未支付的单子
        if (CollUtil.isNotEmpty(orderUnpaidList)) {
            // 按 consumer code 进行分组,
            Map<String, List<OrderUnpaidRelaDTO>> orderUnpaidRelaDTOMap = orderUnpaidList.stream()
                    .collect(Collectors.groupingBy(OrderUnpaidRelaDTO::getConsumerCode));
            for (Map.Entry<String, List<OrderUnpaidRelaDTO>> entry : orderUnpaidRelaDTOMap.entrySet()) {
                Set<String> orderCodeSet = entry.getValue().stream()
                        .map(OrderUnpaidRelaDTO::getOrderCode)
                        .collect(Collectors.toSet());
                if (currentConsumerCode.equals(entry.getKey())) {
                    // 您有未支付的订单，请前往订单详情页完成支付
                    orderCreateRespVO.setHasUnpaidOrder(true);
                    orderCreateRespVO.setOrderCodeList(new ArrayList<>(orderCodeSet));
                    log.info(Constants.ORDER_CREATE_RESPONSE, JSON.toJSONString(orderCreateRespVO));
                    return orderCreateRespVO;
                } else {
                    orderCreateRespVO.setHasUnpaidOrderInOtherWechat(true);
                    log.info(Constants.ORDER_CREATE_RESPONSE, JSON.toJSONString(orderCreateRespVO));
                    return orderCreateRespVO;
                }
            }
        }

        // 是否有已支付但未激活完成, 或售后处理中的订单
        List<OrderUnpaidRelaDTO> orderAfterSalesList = ordersForVCS.stream()
                .filter(dto -> OrderStatusEnum.PAID.getCode().equals(dto.getOrderStatus()) || OrderStatusEnum.AFTER_SALES.getCode().equals(dto.getOrderStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(orderAfterSalesList)) {
            Set<String> orderCodeSet = orderAfterSalesList.stream().map(OrderUnpaidRelaDTO::getOrderCode)
                    .collect(Collectors.toSet());
            buildOrderCreateResp(orderAfterSalesList, orderCreateRespVO);
            log.info("存在已支付但未激活完成, 或售后处理中的订单," + Constants.ORDER_CREATE_RESPONSE, orderCodeSet);
            return orderCreateRespVO;
        }

        return null;
    }

    /**
     * 根据订单列表构建订单创建响应对象
     * 此方法主要根据订单的支付状态和售后状态来设置订单创建响应对象的相关字段
     *
     * @param orderAfterSalesList 一组订单信息，包含订单状态等数据
     * @param orderCreateRespVO 订单创建响应对象，将根据订单状态设置其字段
     */
    private void buildOrderCreateResp(List<OrderUnpaidRelaDTO> orderAfterSalesList, OrderCreateRespVO orderCreateRespVO) {
        // 取其中一个订单状态
        Integer orderStatus = orderAfterSalesList.get(0).getOrderStatus();
        if (OrderStatusEnum.PAID.getCode().equals(orderStatus)) {
            orderCreateRespVO.setHasPaidOrder(true);
            return;
        }
        Integer refundStatus = orderAfterSalesList.get(0).getRefundStatus();
        // 如果是售后处理中需要看退款状态
        if (!RefundStatusEnum.NO_REFUND.getCode().equals(refundStatus)) {
            orderCreateRespVO.setHasRefundedOrder(true);
        } else {
            orderCreateRespVO.setHasAfterSalesOrder(true);
        }
    }

    /**
     * 发送订单超时，取消订单的消息
     *
     * @param orderInfo 订单info
     */
    private void sendTimeoutCancelOrderMsg(OrderInfoDO orderInfo) {
        if (StringUtils.isBlank(orderInfo.getOrderCode())) {
            log.error("订单超时，发送延迟消息，订单号为空");
            return;
        } else if (!StringUtils.equals(BusinessIdEnum.VCS.getCode(), orderInfo.getBusinessCode())) {
            // 非VCS订单（BG、LRE订单）拆单场景下，只从父单维度进行支付和取消订单
            if (!StringUtils.equals(orderInfo.getParentOrderCode(), orderInfo.getOrderCode())) {
                return;
            }
        }

        CancelOrderMessage cancelOrderMessage = new CancelOrderMessage();
        cancelOrderMessage.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        cancelOrderMessage.setParentOrderCode(orderInfo.getParentOrderCode());
        cancelOrderMessage.setOrderCode(orderInfo.getOrderCode());
        cancelOrderMessage.setTenantId(TenantContextHolder.getTenantId());
        cancelOrderMessage.setSendTime(orderInfo.getOrderTime());
        cancelOrderMessage.setBusinessCode(orderInfo.getBusinessCode());
        producerTool.sendMsg(KafkaConstants.ORDER_TIMEOUT_CANCEL_TOPIC, "", JSON.toJSONString(cancelOrderMessage));
        log.info("订单超时取消，发送消息, cancelOrderMessage：{}", cancelOrderMessage);
    }


    /**
     * 异步清除订单提交内的购物车商品项。
     * 此方法将从订单中提取购物车商品项的编码，并异步调用购物车服务以清除这些项。
     *
     * @param orderCreateDTO 订单创建的传输对象
     */
    private void asyncClearShoppingCartItems(OrderCreateDTO orderCreateDTO) {
        //当前租户id
        Long tenantId = TenantContextHolder.getTenantId();
        if (orderCreateDTO != null && !orderCreateDTO.getShopCarItemList().isEmpty()) {
            List<String> cartItemCodes = orderCreateDTO.getShopCarItemList().stream()
                    .map(OrderShopCarItemDTO::getCartItemCode)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(cartItemCodes)) {
                // 异步执行删除操作
                CompletableFuture.runAsync(() -> {
                            // 设置当前线程的租户上下文为提取到的租户ID
                            TenantContextHolder.setTenantId(tenantId);
                            shoppingCarItemService.delete(cartItemCodes);
                        })
                        .exceptionally(ex -> {
                            log.error("Failed to clear shopping cart items asynchronously", ex);
                            return null;
                        });
            }
        }
    }

/*    private void insertStatisticAsync(String consumerCode, List<String> vcsOrderCodeList, Long tenantId, String clientId) {
        CompletableFuture.runAsync(() -> {
                    // 设置当前线程的租户上下文为提取到的租户ID
                    TenantContextHolder.setTenantId(tenantId);
                    insertStatistic(consumerCode, vcsOrderCodeList, clientId);
                })
                .exceptionally(ex -> {
                    log.error("Failed to insert statistics asynchronously", ex);
                    return null;
                });
    }*/

    /**
     * 异步插入统计表
     */
    private void insertStatisticAsync(String consumerCode, List<VCSOrderInfoDO> vcsStatisticList, Long tenantId, String clientId) {
        CompletableFuture.runAsync(() -> {
                    // 设置当前线程的租户上下文为提取到的租户ID
                    TenantContextHolder.setTenantId(tenantId);
                    insertStatistic(consumerCode, vcsStatisticList, clientId);
                })
                .exceptionally(ex -> {
                    log.error("Failed to insert statistics asynchronously", ex);
                    return null;
                });
    }

    /**
     * 处理不同业务线的拆单逻辑
     */
    private OrderCreateRespVO processBusinessSplit(OrderCreateDTO orderCreateDTO, String parentOrderCode, String oneID, LocalDateTime now, String clientId) {
        // 构造响应对象
        OrderCreateRespVO response = new OrderCreateRespVO();
        response.setParentOrderCode(parentOrderCode);
        // 第一层拆分逻辑：根据业务线拆分
        Map<String, List<OrderShopCarItemDTO>> groupByBusiness = orderCreateDTO.getShopCarItemList().stream()
                .collect(Collectors.groupingBy(OrderShopCarItemDTO::getBusinessName));
        groupByBusiness.forEach((business, items) -> {
            // 处理VCS业务线
            if (BusinessNameEnum.VCS.getName().equals(business.toUpperCase())) {
                OrderCreateRespVO vcsResponse = processVcsSplit(orderCreateDTO, items, parentOrderCode, oneID, now, clientId);
                response.setOrderCodeList(vcsResponse.getOrderCodeList());
            } else {
                // 处理非VCS业务线
                log.info("处理非VCS拆单逻辑");
            }
        });
        return response;
    }

    /**
     * 处理VCS业务线的拆单逻辑
     */
    private OrderCreateRespVO processVcsSplit(OrderCreateDTO orderCreateDTO, List<OrderShopCarItemDTO> vcsItems, String parentOrderCode, String oneID,
                                              LocalDateTime now, String clientId) {
        log.info("处理VCS拆单逻辑, orderCreateDTO:{}, vcsItems:{}, parentOrderCode:{}, oneID:{}, now:{}, clientId:{}", JSON.toJSONString(orderCreateDTO), JSON.toJSONString(vcsItems), parentOrderCode, oneID, now, clientId);

        //当前租户id；渠道编码；消费者编码；判断是否为代客下单
        Long tenantId = TenantContextHolder.getTenantId();
        String channelCode = orderCreateDTO.getGlobalInfoDTO().getChannelCode();
        String consumerCode = orderCreateDTO.getGlobalInfoDTO().getConsumerCode();
        boolean isCustomerServiceOrder = CUSTOMER_SERVICE_ORDER.equals(consumerCode);

        // 按车辆VIN分组,key是车辆VIN::商品类型，value是具有相同VIN和商品类型的OrderShopCarItemDTO对象列表
        Map<String, List<OrderShopCarItemDTO>> vcsItemsByVinAndType = vcsItems.stream()
                .collect(Collectors.groupingBy(item -> item.getCarVin() + CONCAT_SYMBOL + item.getCartItemType()));

        // 初始化序列号 for订单号
        int sequenceNumber = 1;
        //创建入库对象容器
        List<OrderInfoDO> allSubOrders = new ArrayList<>();
        List<OrderStatusLogDO> allOrderStatusLogList = new ArrayList<>();
        List<OrderItemDO> allOrderItemList = new ArrayList<>();
        List<OrderTermsDO> allOrderTermsList = new ArrayList<>();
        List<VCSOrderInfoDO> allVcsOrderInfoList = new ArrayList<>();
        // 待添加地址信息的子订单列表
        List<String> childOrderCodeListForGift = new ArrayList<>();
        List<CustomerServiceOrderDO> allCustomerServiceOrderList = new ArrayList<>();
        List<ValetOrderMessage> allValetOrderMessageList = new ArrayList<>();

        for (Map.Entry<String, List<OrderShopCarItemDTO>> entry : vcsItemsByVinAndType.entrySet()) {
            String[] split = entry.getKey().split(CONCAT_SYMBOL);
            String carVin = split[0];
            OrderShopCarItemDTO carItemDTO = entry.getValue().get(0); //TODO 暂时 key为vin::商品类型下 商品List<item> 只有一个item
            String brandCode = carItemDTO.getItemBrandCode();
            // 生成子单号并组装t_order_info
            String childOrderCode = OrderCodeGenerator.generateChildOrderCode(
                    oneID,
                    brandCode,
                    channelCode,
                    getFulfillmentCodeByBusinessName(true, carItemDTO),
                    SequenceNumberGenerator.generate(sequenceNumber++)
            );
            // a.组装子订单
            OrderInfoDO childOrderInfo = buildOrderInfo(orderCreateDTO, carItemDTO, childOrderCode, parentOrderCode, now);
            // 只有配置了赠品功能的商品才需要入库
            Boolean giftEnable = carItemDTO.getGiftEnable();
            if (Objects.nonNull(giftEnable) && Boolean.TRUE.equals(giftEnable)) {
                childOrderCodeListForGift.add(childOrderCode);
            } else {
                childOrderInfo.setGiftAddress(GiftAddressEnum.NOT_REQUIRED.getCode());
            }
            //重新设置子订单的金额        TODO 运费暂时没有
            BigDecimal salePriceTotal = calculateOrderAmountBySalePrice(entry.getValue());
            BigDecimal payPriceTotal = calculateOrderAmountByPayPrice(entry.getValue());
            childOrderInfo.setOriginalFeeTotalAmount(salePriceTotal.intValue()); // 原始订单金额 originalFeeTotalAmount = SUM(sale_price*quantity)
            childOrderInfo.setFeeTotalAmount(salePriceTotal.intValue()); // 应付金额 feeTotalAmount = SUM(sale_price*quantity )
            childOrderInfo.setCostAmount(payPriceTotal.intValue()); // 实付金额 costAmount = SUM(payPrice*quantity )
            childOrderInfo.setDiscountTotalAmount(childOrderInfo.getFeeTotalAmount() - childOrderInfo.getCostAmount()); // 折扣金额 discountAmount = feeTotalAmount - costAmount
            //设置税额相关两个字段
            buildTaxAmount(childOrderInfo, carItemDTO);

            // b.组装t_order_status_log
            OrderStatusLogDO orderStatusLogDO = buildOrderStatusLog(childOrderCode, now);

            // c.组装t_order_item，存储当前子订单的所有 OrderItemDO 对象
            for (OrderShopCarItemDTO itemDTO : entry.getValue()) {
                OrderItemDO orderItemDO = buildOrderItem(itemDTO, childOrderCode);
                String orderItemCode = orderItemDO.getOrderItemCode();

                //d. 组装t_order_terms
                List<OrderTermsDO> orderTermsDOS = buildOrderTerms(consumerCode, itemDTO, childOrderCode, now, orderItemCode);

                // 添加到对象容器
                allOrderItemList.add(orderItemDO);
                allOrderTermsList.addAll(orderTermsDOS);

                // 只有商品类型为Remote或PIVI，才入vcs_order表
                if (CartItemTypeEnum.isSingleVcs(itemDTO.getCartItemType())) {
                    //e. 组装t_vcs_order_info
                    VCSOrderInfoDO vcsOrderInfo = buildVCSOrderInfo(
                            itemDTO.getSeriesCode(),
                            itemDTO.getSeriesName(),
                            carVin,
                            itemDTO.getCartItemType(),
                            orderCreateDTO,
                            childOrderCode,
                            orderItemCode,
                            consumerCode);
                    allVcsOrderInfoList.add(vcsOrderInfo);
                }

                // 组装代客下单ValetOrderMessage 信息
                if (isCustomerServiceOrder) {
                    String receivePhone = orderCreateDTO.getCustomerServiceOrderDTO().getReceivePhone();
                    String messageTemplateCode = orderCreateDTO.getCustomerServiceOrderDTO().getMessageTemplateCode();
                    String productName = orderItemDO.getProductName();
                    ValetOrderMessage valetOrderMessage = buildValetOrderMessage(receivePhone, messageTemplateCode, tenantId, productName, childOrderInfo, carVin);
                    allValetOrderMessageList.add(valetOrderMessage);
                }

                // f.遍历其下组合商品list并组装OrderItemDO对象
                buildChildDOList(orderCreateDTO, childOrderCode, true, itemDTO, consumerCode, allVcsOrderInfoList, allOrderItemList);
            }

            // g.组装t_customer_service_order
            CustomerServiceOrderDO customerServiceOrderDO = buildCustomerServiceOrder(orderCreateDTO, childOrderCode, isCustomerServiceOrder);

            // 添加到对象容器
            allCustomerServiceOrderList.add(customerServiceOrderDO);
            allSubOrders.add(childOrderInfo);
            allOrderStatusLogList.add(orderStatusLogDO);
        }

        // 批量插入五个主表
        orderInfoDOMapper.insertBatch(allSubOrders);
        orderStatusLogDOMapper.insertBatch(allOrderStatusLogList);
        orderItemDOService.saveBatch(allOrderItemList);
        orderTermsDOService.saveBatch(allOrderTermsList);
        vcsOrderInfoDOService.saveBatch(allVcsOrderInfoList);
        // 插入订单赠品信息
        orderGiftAddressService.saveOrderGiftAddress(orderCreateDTO.getGiftInfoDTO(), childOrderCodeListForGift);
        // 批量插入代客下单信息,过滤掉null元素
        List<CustomerServiceOrderDO> nonNullCustomerServiceOrders = allCustomerServiceOrderList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(nonNullCustomerServiceOrders)) {
            customerServiceOrderDOService.saveBatch(nonNullCustomerServiceOrders);
        }

        // 代客下单此刻暂不插入异步统计信息，交由回绑consumerCode到订单信息时去完成
        insertStatisticInfo(clientId, isCustomerServiceOrder, allVcsOrderInfoList, consumerCode, tenantId);

        // 发送超时取消订单消息
        for (OrderInfoDO orderInfoDO : allSubOrders) {
            sendTimeoutCancelOrderMsg(orderInfoDO);
        }

        //代客下单时，传递参数，便于notification服务 生成短链发送短信
        sendValetOrderMessage(allValetOrderMessageList);

        // 构造响应对象
        OrderCreateRespVO response = new OrderCreateRespVO();
        response.setOrderCodeList(allSubOrders.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList()));
        response.setParentOrderCode(parentOrderCode);
        return response;
    }

    /**
     * 插入统计信息到统计表中
     * 此方法根据是否是客户服务中心订单来决定是否插入统计信息
     * 如果不是客户服务中心订单，则计算统计信息并异步插入统计表
     *
     * @param clientId 客户端ID，用于标识插入统计信息的客户端
     * @param isCustomerServiceOrder 布尔值，指示是否是客户服务中心订单
     * @param allVcsOrderInfoList 包含所有VCS订单信息的列表
     * @param consumerCode 消费者代码，用于标识统计信息的消费者
     * @param tenantId 租户ID，用于标识插入统计信息的租户
     */
    private void insertStatisticInfo(String clientId, boolean isCustomerServiceOrder, List<VCSOrderInfoDO> allVcsOrderInfoList, String consumerCode, Long tenantId) {
        if (!isCustomerServiceOrder) {
            // 获取各个seriesCode对应的vcs商品订单数量
            List<VCSOrderInfoDO> vcsStatisticList = getVcsOrderInfoDOS(allVcsOrderInfoList, consumerCode);
            log.info("待更新的vcs订单统计数, vcsStatisticList={}", vcsStatisticList);
            // 异步插入统计表
            insertStatisticAsync(consumerCode, vcsStatisticList, tenantId, clientId);
        } else {
            log.info("代客下单, 不插入统计表");
        }
    }

    /**
     * 构造代客下单信息
     */
    private CustomerServiceOrderDO buildCustomerServiceOrder(OrderCreateDTO orderCreateDTO, String childOrderCode, boolean isCustomerServiceOrder) {
        if (!isCustomerServiceOrder) {
            return null;
        }
        CustomerServiceOrderDO customerServiceOrderDO = new CustomerServiceOrderDO();

        // 业务主键order_id：雪花算法
        customerServiceOrderDO.setOrderId(String.valueOf(ecpIdUtil.nextId()));
        // 挂载子订单号
        customerServiceOrderDO.setOrderCode(childOrderCode);
        // 接收付款短信手机号
        customerServiceOrderDO.setRecievePhone(orderCreateDTO.getCustomerServiceOrderDTO().getReceivePhone());
        // 接收短信模板
        customerServiceOrderDO.setMessageTemplateCode(orderCreateDTO.getCustomerServiceOrderDTO().getMessageTemplateCode());
        // 创建人
        customerServiceOrderDO.setCreateOperator(WebFrameworkUtils.getLoginUserName());
        // 是否绑定客户
        customerServiceOrderDO.setBindCustomer(BindCustomerEnum.NO.getCode());

        return customerServiceOrderDO;
    }

    /**
     * 计算子订单金额的辅助方法
     *
     * @param items 子订单的商品项列表
     * @return
     */
    private BigDecimal calculateOrderAmountBySalePrice(List<OrderShopCarItemDTO> items) {
        return items.stream()
                .map(item -> MoneyUtil.convertToCents(item.getSalePrice()).multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 用 payPrice 计算子订单金额的辅助方法
     */
    private BigDecimal calculateOrderAmountByPayPrice(List<OrderShopCarItemDTO> items) {
        return items.stream()
                .map(item -> MoneyUtil.convertToCents(item.getPayPrice()).multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 将订单项按履约类型分类存储在Map中。
     *
     * @param items
     * @return
     */
    private Map<Integer, List<OrderShopCarItemDTO>> categorizeItemsByFulfillmentType(List<OrderShopCarItemDTO> items) {
        return items.stream()
                .collect(Collectors.groupingBy(OrderShopCarItemDTO::getCartItemType));
    }

    /**
     * @param orderCreateDTO
     * @param oneID
     * @param now
     * @return
     */
    private OrderInfoDO createParentOrder(OrderCreateDTO orderCreateDTO, String oneID, LocalDateTime now) {
        // 获取品牌编码和渠道编码
        String brandCode = orderCreateDTO.getGlobalInfoDTO().getGlobalBrandCode();
        String channelCode = orderCreateDTO.getGlobalInfoDTO().getChannelCode();
        OrderShopCarItemDTO carItemDTO = orderCreateDTO.getShopCarItemList().get(0);

        // 父订单号的生成逻辑
        String parentOrderCode = OrderCodeGenerator.generateParentOrderCode(
                oneID,
                brandCode,
                channelCode
        );

        //问题2.parentOrder这条记录下的parent_order_code字段，是自己
        OrderInfoDO parentOrderInfo = buildOrderInfo(orderCreateDTO, carItemDTO, parentOrderCode, parentOrderCode, now);
        // 保存税额
        buildTaxAmount(parentOrderInfo, carItemDTO);
        //问题1.parentOrder这条记录下的order_type字段，存0:聚合父订单
        parentOrderInfo.setOrderType(OrderFulfillmentTypeEnum.AGGREGATE_PARENT_ORDER.getFulfilmentType());

        return parentOrderInfo;
    }

    /**
     * 处理不需要拆分的订单。
     * -当所有商品项属于同一履约类型
     * -且如果是vsc履约类型,具有相同的carVin时调用此方法。
     *
     * @param orderCreateDTO 订单创建DTO
     * @param oneID          用户ID或其他唯一标识
     * @param now            当前时间
     * @param clientId       clientId
     * @return OrderCreateRespVO 创建订单的响应对象
     */
    private OrderCreateRespVO processSingleFulfillmentOrder(OrderCreateDTO orderCreateDTO, String oneID, LocalDateTime now, String clientId) {
        log.info("处理不需要拆分的订单, orderCreateDTO:{}, oneID:{}, now:{}, clientId:{}", JSON.toJSONString(orderCreateDTO), oneID, now, clientId);

        // 判断是否为代客下单; 初始化序列号; 当前租户id; 获取品牌编码和渠道编码; consumerCode
        boolean isCustomerServiceOrder = CUSTOMER_SERVICE_ORDER.equals(oneID);
        int sequenceNumber = 1;
        Long tenantId = TenantContextHolder.getTenantId();
        String brandCode = orderCreateDTO.getGlobalInfoDTO().getGlobalBrandCode();
        String channelCode = orderCreateDTO.getGlobalInfoDTO().getChannelCode();
        String consumerCode = orderCreateDTO.getGlobalInfoDTO().getConsumerCode();
        // 不拆单的情况shopCarItemList只会有一个item：一车一种类型的商品item
        OrderShopCarItemDTO carItemDTO = orderCreateDTO.getShopCarItemList().get(0);
        String carVin = carItemDTO.getCarVin();

        // 判断是否为VCS业务线
        boolean isVcsBusiness = StringUtils.isNotBlank(carItemDTO.getBusinessName()) &&
                BusinessNameEnum.VCS.getName().equals(carItemDTO.getBusinessName().toUpperCase());

        List<String> childOrderCodeListForGift = new ArrayList<>();
        // 生成子订单号（同时也是父订单号）
        String orderCode = OrderCodeGenerator.generateChildOrderCode(
                oneID,
                brandCode,
                channelCode,
                getFulfillmentCodeByBusinessName(isVcsBusiness, carItemDTO),
                // 生成序列号
                SequenceNumberGenerator.generate(sequenceNumber)
        );
        // 父订单号即为自己的订单号
        //a.生成并组装OrderInfoDO对象
        OrderInfoDO orderInfo = buildOrderInfo(orderCreateDTO, carItemDTO, orderCode, orderCode, now);
        // 设置t_order_info两个税额字段
        buildTaxAmount(orderInfo, carItemDTO);
        // 只有配置了赠品功能的商品才需要入库
        Boolean giftEnable = carItemDTO.getGiftEnable();
        if (Objects.nonNull(giftEnable) && Boolean.TRUE.equals(giftEnable)) {
            childOrderCodeListForGift.add(orderCode);
        } else {
            orderInfo.setGiftAddress(GiftAddressEnum.NOT_REQUIRED.getCode());
        }

        //b.生成并组装OrderStatusLog对象
        OrderStatusLogDO orderStatusLogDO = buildOrderStatusLog(orderCode, now);

        //准备存入数据库的list容器
        List<OrderItemDO> orderItemList = new ArrayList<>();
        List<OrderTermsDO> orderTermsDOS;
        List<VCSOrderInfoDO> vcsOrderInfos = new ArrayList<>();

        //c.遍历item list并组装OrderItemDO对象
        OrderItemDO orderItem = buildOrderItem(carItemDTO, orderCode);
        String orderItemCode = orderItem.getOrderItemCode();

        //d.生成并组装OrderTerms对象
        orderTermsDOS = buildOrderTerms(consumerCode, carItemDTO, orderCode, now, orderItemCode);

        //e.生成并组装VCSOrderInfo对象（如果业务线是VCS且不是实物商品也不是组合商品，且car_vin不为空才去组装VCSOrderInfo）
        if (isVcsBusiness && CartItemTypeEnum.isSingleVcs(carItemDTO.getCartItemType())
                && StringUtils.isNotBlank(carItemDTO.getCarVin())) {
            VCSOrderInfoDO vcsOrderInfo = buildVCSOrderInfo(
                    carItemDTO.getSeriesCode(),
                    carItemDTO.getSeriesName(),
                    carVin,
                    carItemDTO.getCartItemType(),
                    orderCreateDTO,
                    orderCode,
                    orderItemCode,
                    consumerCode);
            vcsOrderInfos.add(vcsOrderInfo);
        }

        orderItemList.add(orderItem);
        // f.遍历其下组合商品list并组装OrderItemDO对象
        buildChildDOList(orderCreateDTO, orderCode, isVcsBusiness, carItemDTO, consumerCode, vcsOrderInfos, orderItemList);

        // g.插入代客下单信息;
        CustomerServiceOrderDO customerServiceOrderDO = buildCustomerServiceOrder(orderCreateDTO, orderCode, isCustomerServiceOrder);

        // 批量插入五张主表
        orderInfoDOMapper.insert(orderInfo);
        orderStatusLogDOMapper.insert(orderStatusLogDO);
        orderItemDOService.saveBatch(orderItemList);
        orderTermsDOService.saveBatch(orderTermsDOS);
        vcsOrderInfoDOService.saveBatch(vcsOrderInfos);
        // 插入订单赠品信息
        orderGiftAddressService.saveOrderGiftAddress(orderCreateDTO.getGiftInfoDTO(), childOrderCodeListForGift);
        // 插入代客下单信息
        if (customerServiceOrderDO != null) {
            customerServiceOrderDOMapper.insert(customerServiceOrderDO);
        }

        // 检查consumerCode是否为代客下单标识, 代客下单此刻暂不插入异步统计信息
        if (!isCustomerServiceOrder) {
            // 获取各个seriesCode对应的vcs商品订单数量
            List<VCSOrderInfoDO> vcsStatisticList = getVcsOrderInfoDOS(vcsOrderInfos, consumerCode);
            log.info("待更新的vcs订单统计数, vcsStatisticList={}", vcsStatisticList);
            // 异步插入统计表
            insertStatisticAsync(consumerCode, vcsStatisticList, tenantId, clientId);
        } else {
            log.info("Consumer code is {}, skipping async statistic insertion.", CUSTOMER_SERVICE_ORDER);
        }

        sendTimeoutCancelOrderMsg(orderInfo);
        log.info("发送订单超时取消信息，orderInfo{}", orderInfo);

        //代客下单时，传递参数，便于notification服务 生成短链发送短信
        if (isCustomerServiceOrder) {
            String receivePhone = orderCreateDTO.getCustomerServiceOrderDTO().getReceivePhone();
            String messageTemplateCode = orderCreateDTO.getCustomerServiceOrderDTO().getMessageTemplateCode();
            String productName = orderItem.getProductName();
            ValetOrderMessage valetOrderMessage = buildValetOrderMessage(receivePhone, messageTemplateCode, tenantId, productName, orderInfo, carVin);
            sendValetOrderMessage(Collections.singletonList(valetOrderMessage));
        }

        // 构造响应对象
        OrderCreateRespVO response = new OrderCreateRespVO();
        response.setParentOrderCode(orderCode);
        response.setOrderCodeList(new ArrayList<>(Collections.singletonList(orderCode)));
        return response;
    }

    private void sendValetOrderMessage(List<ValetOrderMessage> allValetOrderMessageList) {
        if(CollUtil.isNotEmpty(allValetOrderMessageList)){
            for (ValetOrderMessage valetOrderMessage : allValetOrderMessageList) {
                producerTool.sendMsg(KafkaConstants.ORDER_BEHALFOF_CREATE_TOPIC, "", JSON.toJSONString(valetOrderMessage));
            }
            log.info("send ValetOrderMessage msg:{}", JSON.toJSONString(allValetOrderMessageList));
        }
    }

    /**
     * 组装组合商品itemDO
     */
    private void buildChildDOList(OrderCreateDTO orderCreateDTO, String orderCode, boolean isVcsBusiness, OrderShopCarItemDTO carItemDTO, String consumerCode,
                                  List<VCSOrderInfoDO> vcsOrderInfos, List<OrderItemDO> orderItemList) {
        for (OrderShopCarItemDTO itemDTO : carItemDTO.getChildList()) {
            OrderItemDO orderItem = buildOrderItem(itemDTO, orderCode);
            String orderItemCode = orderItem.getOrderItemCode();
            // 生成并组装VCSOrderInfo对象（如果业务线是VCS且不是实物商品也不是组合商品，且car_vin不为空才去组装VCSOrderInfo）
            if (isVcsBusiness && CartItemTypeEnum.isSingleVcs(itemDTO.getCartItemType())
                    && StringUtils.isNotBlank(itemDTO.getCarVin())) {
                VCSOrderInfoDO vcsOrderInfo = buildVCSOrderInfo(
                        itemDTO.getSeriesCode(),
                        itemDTO.getSeriesName(),
                        itemDTO.getCarVin(),
                        itemDTO.getCartItemType(),
                        orderCreateDTO,
                        orderCode,
                        orderItemCode,
                        consumerCode);
                vcsOrderInfos.add(vcsOrderInfo);
            }
            orderItemList.add(orderItem);
        }
    }

    /**
     * 获取各个seriesCode对应的vcs商品订单数量
     */
    private List<VCSOrderInfoDO> getVcsOrderInfoDOS(List<VCSOrderInfoDO> vcsOrderInfos, String consumerCode) {
        if (!vcsOrderInfos.isEmpty()) {
            Map<String, String> seriesMap = vcsOrderInfos.stream().collect(Collectors.toMap(VCSOrderInfoDO::getSeriesCode, VCSOrderInfoDO::getSeriesName, (o, n) -> o));
            Map<String, Long> seriesCodeCountMap = vcsOrderInfos.stream().collect(Collectors.groupingBy(VCSOrderInfoDO::getSeriesCode, Collectors.counting()));
            // 查询vcs初始统计信息
            // 查询vcs初始统计信息
            List<VCSOrderInfoDO> vcsCountList = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                    .eq(VCSOrderInfoDO::getConsumerCode, consumerCode)
                    .in(VCSOrderInfoDO::getSeriesCode, seriesCodeCountMap.keySet())
                    .eq(BaseDO::getIsDeleted, false)
                    .select(VCSOrderInfoDO::getSeriesCode));
            Map<String, Long> vcsCountMap = vcsCountList.stream()
                    .collect(Collectors.groupingBy(VCSOrderInfoDO::getSeriesCode, Collectors.counting()));
            // 创建订单后的统计信息
            return seriesCodeCountMap.entrySet().stream().map(entry -> {
                String seriesCode = entry.getKey();
                int originCount = 0;
                if (CollUtil.isNotEmpty(vcsCountMap) && Objects.nonNull(vcsCountMap.get(seriesCode))) {
                    originCount = vcsCountMap.get(seriesCode).intValue();
                }
                VCSOrderInfoDO vcsOrderInfoDO = new VCSOrderInfoDO();
                vcsOrderInfoDO.setSeriesName(seriesMap.get(seriesCode));
                vcsOrderInfoDO.setSeriesCode(seriesCode);
                vcsOrderInfoDO.setCount(entry.getValue().intValue() + originCount);
                return vcsOrderInfoDO;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 保存税额
     */
    private static void buildTaxAmount(OrderInfoDO orderInfo, OrderShopCarItemDTO carItemDTO) {
        // 设置不含税总金额和税费总金额
        Integer excludeTaxAmount = MoneyUtil.calculateExcludeTaxAmount(carItemDTO.getTaxRate(), orderInfo.getCostAmount());
        Integer taxAmount = orderInfo.getCostAmount() - excludeTaxAmount;
        orderInfo.setExcludeTaxAmount(excludeTaxAmount);
        orderInfo.setTaxAmount(taxAmount);
    }

    private VCSOrderInfoDO buildVCSOrderInfo(String seriesCode, String seriesName, String carVin, Integer cartItemType,
                                             OrderCreateDTO orderCreateDTO, String orderCode, String orderItemCode, String consumerCode) {
        VCSOrderInfoDO vcsOrderInfo = new VCSOrderInfoDO();
        // 使用雪花算法生成vcs_order_code
        vcsOrderInfo.setVcsOrderCode(String.valueOf(ecpIdUtil.nextId()));

        vcsOrderInfo.setOrderCode(orderCode);
        vcsOrderInfo.setOrderItemCode(orderItemCode);
        vcsOrderInfo.setConsumerCode(consumerCode);
        // 存 半隐藏 MD5 和加密值三种
        String incontrolId = orderCreateDTO.getOrderInfoDTO().getIncontrolId();
        if (CharSequenceUtil.isNotBlank(incontrolId)) {
            vcsOrderInfo.setIncontrolId(phoneNumberDecodeUtil.getEncryptText(incontrolId));
            vcsOrderInfo.setIncontrolIdMix(phoneNumberDecodeUtil.getIncontrolIdMix(incontrolId));
            vcsOrderInfo.setIncontrolIdMd5(SecureUtil.md5(incontrolId));
        }

        vcsOrderInfo.setCarVin(phoneNumberDecodeUtil.getEncryptText(carVin));
        vcsOrderInfo.setCarVinView(carVin);
        vcsOrderInfo.setCarVinMix(phoneNumberDecodeUtil.getVinMix(carVin));
        vcsOrderInfo.setCarVinMd5(SecureUtil.md5(carVin));
        vcsOrderInfo.setSeriesCode(seriesCode);
        vcsOrderInfo.setSeriesName(seriesName);
        vcsOrderInfo.setServiceType(cartItemType);
        return vcsOrderInfo;
    }

    /**
     * 条款与子订单中的商品进行挂钩
     * TODO 暂时一个子订单（即按car_vin进行拆分，一车一子订单）只能购买一个商品
     *
     * @return
     */
    private List<OrderTermsDO> buildOrderTerms(String consumerCode, OrderShopCarItemDTO itemDTO, String orderCode, LocalDateTime now, String orderItemCode) {
        // 初始化OrderTermsDO列表
        List<OrderTermsDO> orderTermsList = new ArrayList<>();

        for (String policyCode : itemDTO.getPolicyCodeList()) {
            OrderTermsDO orderTerms = new OrderTermsDO();

            orderTerms.setOrderCode(orderCode);
            orderTerms.setOrderItemCode(orderItemCode);
            orderTerms.setTermsCode(policyCode);
            orderTerms.setSignTime(now);
            orderTerms.setConsumerCode(consumerCode);

            // 添加到对象列表
            orderTermsList.add(orderTerms);
        }

        return orderTermsList;
    }

    private OrderStatusLogDO buildOrderStatusLog(String orderCode, LocalDateTime now) {
        OrderStatusLogDO orderStatusLogDO = new OrderStatusLogDO();

        orderStatusLogDO.setOrderCode(orderCode);
        // 第一次下单 初始 before、after状态都是ORDERED
        orderStatusLogDO.setBeforeStatus(OrderStatusEnum.ORDERED.getCode());
        orderStatusLogDO.setAfterStatus(OrderStatusEnum.ORDERED.getCode());
        orderStatusLogDO.setChangeTime(now);

        return orderStatusLogDO;
    }

    @Override
    public PageResult<OrderInfoPageVO> getPage(OrderPageReqDTO dto) {
        //orderCode 查询逻辑,先查询parentCode,以parentCode去匹配
        if (StringUtils.isNotBlank(dto.getOrderCode())) {
            OrderInfoDO orderInfoDO = orderInfoDOMapper.getParentCode(dto.getOrderCode());
            if (orderInfoDO != null) {
                dto.setOrderCode(orderInfoDO.getParentOrderCode());
            }
        }
        if (StringUtils.isNotBlank(dto.getOrderStatus())) {
            List<Integer> refundOrderStatusList = Arrays.stream(dto.getOrderStatus().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            dto.setStatusList(refundOrderStatusList);
        }
        if (StringUtils.isNotBlank(dto.getMobile())) {
            dto.setMobile(SecureUtil.md5(dto.getMobile()));
        }
        if (StringUtils.isNotBlank(dto.getInControlId())) {
            dto.setInControlId(SecureUtil.md5(dto.getInControlId()));
        }
        if (StringUtils.isNotBlank(dto.getVin())) {
            dto.setVin(SecureUtil.md5(dto.getVin()));
        }
        // 获取订单来源
        if (Objects.nonNull(dto.getOrderSource())) {
            List<Integer> orderChannel = OrderSourceEnum.buildOrderChannel(dto.getOrderSource());
            dto.setOrderChannelList(orderChannel);
        }
        //设置business code 只查询VCS数据
        dto.setBusinessCode(BusinessIdEnum.VCS.getCode());

        Page<OrderPageReqDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        Page<OrderInfoPageVO> pageResult;
        Boolean valetPermission = securityFrameworkService.hasAnyPermissions(String.valueOf(getLoginUserId()), VALET_ORDER_LIST_PERMISSION);
        Boolean vcsOrderListPermission = securityFrameworkService.hasAnyPermissions(String.valueOf(getLoginUserId()), ORDER_PAGE_LIST_PERMISSION);
        log.info("VCS订单列表页, valetPermission:{}, vcsOrderListPermission:{}", valetPermission, vcsOrderListPermission);
        if (Boolean.TRUE.equals(valetPermission) && Boolean.FALSE.equals(vcsOrderListPermission)) {
            dto.setOrderCreator(WebFrameworkUtils.getLoginUserName());
            log.info("VCS订单列表页, dto:{}", dto);
            pageResult = orderInfoDOMapper.getValetPage(page, dto);
        } else {
            pageResult = orderInfoDOMapper.getPage(page, dto);
        }
        List<OrderInfoPageVO> list = pageResult.getRecords();
        List<OrderInfoPageVO> result = new ArrayList<>();
        handleList(list, result);

        return new PageResult<>(result, pageResult.getTotal());
    }

    @Override
    public PageResult<OrderNewBrandRespVO> getOrderPage(String consumerCode, OrderPageReqDTO dto) {
        //通过主单查询（关联ITEM VCS表）所有订单(要注意支付和未支付状态下拉的单子不一样）
        PageResult<OrderNewBrandRespVO> result = new PageResult<>();

        //0 先查询主表信息
        // 创建分页对象
        Page<OrderInfoDO> pageParam = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<OrderInfoDO> orderInfoPage = orderInfoDOMapper.selectConsumerOrderPage(pageParam, consumerCode, dto.getOrderStatus());
        List<OrderInfoDO> orderInfoDOList = orderInfoPage.getRecords();
        if (CollUtil.isEmpty(orderInfoDOList)) {
            log.info("查询订单主表时, 主表信息为空, 当前查询用户是:{}", consumerCode);
            return result;
        }

        //现在LRE和BG商品可以组成混合父单，所以还要在查一下有没有这种单，需要把子单的所有订单行找出来
        List<String> orderCodeList = orderInfoDOList.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
        Map<String, List<String>> parentOrderAndHisChild = new HashMap<>();
        List<OrderInfoDO> allOrderCodeListIncludeParent = orderInfoDOMapper.selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                .in(OrderInfoDO::getParentOrderCode, orderCodeList)
                .apply("order_code != parent_order_code")  // 不要父订单是自己的
                .eq(OrderInfoDO::getIsDeleted, false)
        );// 新增条件：orderCode不等于parentOrderCode
        putParentCodeAndCodeList(allOrderCodeListIncludeParent, orderCodeList, parentOrderAndHisChild);

        //order item 和VCS的信息
        List<OrderItemRefundVcsPo> orderItemAndVcsInfo = orderInfoDOMapper.selectOrderItemInfo(orderCodeList);
        if (CollUtil.isEmpty(orderItemAndVcsInfo)){
            log.info("根据主单code查询子表时，数据查询为空，主单code数量为:{}", orderInfoDOList.size());
            return result;
        }

        // c.得到所有orderItemcodes对应的退款状态  VCS专有
        List<String> orderItemCodeList = orderItemAndVcsInfo.stream().map(OrderItemRefundVcsPo::getOrderItemCode).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(orderItemCodeList)){
            log.warn("用户{}的order item code为空",consumerCode);
            return result;
        }


        setBaseCatInfo(consumerCode, orderInfoDOList, orderItemAndVcsInfo);

        List<OrderRefundStatusMapDTO> refundOrderStatusByOrderItemCodeList = orderRefundItemDOMapper.getRefundInfoByOrderItemCodeList(orderItemCodeList);
        //Map<String, OrderRefundStatusMapDTO> refundOrderInfoMap = refundOrderStatusByOrderItemCodeList.stream().collect(Collectors.toMap(OrderRefundStatusMapDTO::getOrderItemCode, Function.identity()));
        Map<String, List<OrderRefundStatusMapDTO>> refundOrderInfoMap = refundOrderStatusByOrderItemCodeList.stream().collect(Collectors.groupingBy(OrderRefundStatusMapDTO::getOrderItemCode));

        //查询LRE商品订单详细信息
        Map<String, List<OrderCouponDetailDO>> couponOrderItemMapByItemCode = getCouponOrderItemMapByItemCode(orderCodeList);

        // d.查t_order_status_mapping的全量
        List<OrderStatusMappingDO> orderStatusMappingDOList = orderStatusMappingDOMapper.selectList(new LambdaQueryWrapper<OrderStatusMappingDO>()
                .eq(OrderStatusMappingDO::getIsDeleted, 0));
        //根据order_status和refund_order_status 作为一个组合key进行分组
        Map<String, OrderStatusMappingDO> orderStatusMappingMap = orderStatusMappingDOList.stream()
                .collect(Collectors.toMap(item -> item.getOrderStatus() + "_" + item.getRefundOrderStatus(), Function.identity()));

        // e.批量查询订单开票状态
        Map<String, InvoiceStatusVO> invoiceStatusVOMap = getInvoiceStatusMap2(orderInfoDOList);

        // sprint47 f.根据orderInfoDOS中 orderCode对应的orderStatus 去查t_feedback_records是否有 这个订单当前环节的评价记录
        List<FeedbackRecordsDO> feedbackRecordsDOList = feedbackRecordsDOMapper.selectList(new LambdaQueryWrapper<FeedbackRecordsDO>()
                .in(FeedbackRecordsDO::getOrderCode, orderCodeList)
                .eq(FeedbackRecordsDO::getIsDeleted, 0));
        // 构建 Map<String, List<FeedbackRecordsDO>>，key 是 orderCode，value 是该订单的所有评价记录
        Map<String, List<FeedbackRecordsDO>> feedbackRecordsMap = feedbackRecordsDOList.stream().collect(Collectors.groupingBy(FeedbackRecordsDO::getOrderCode));

        // g. 查是否有启用的 评价配置
        List<FeedbackConfigDO> feedbackConfigDOList = feedbackConfigDOMapper.selectList(new LambdaQueryWrapper<FeedbackConfigDO>()
                .eq(FeedbackConfigDO::getIsDeleted, 0)
                .eq(FeedbackConfigDO::getEnableStatus, FeedBackEnableStatusEnum.ENABLE.getCode()));

        List<String> carVinList = orderItemAndVcsInfo.stream().map(OrderItemRefundVcsPo::getCarVin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        //h.获取sku商品列表信息，这里要获取历史

        //查询商品订单使用的优惠券记录
        Map<String, List<OrderDiscountDetailDO>> couponDiscountItemMapByItemCode = getCouponDiscountItemMapByItemCode(orderCodeList);

        // 过滤出订单中为组合商品的订单
        Set<String> bundleOrderCodeSet = orderInfoDOList.stream().filter(orderInfoDO -> OrderTypeEnum.BUNDLED_GOODS.getCode().equals(orderInfoDO.getOrderType()))
                .map(OrderInfoDO::getOrderCode).collect(Collectors.toSet());
        // 存在组合商品，需要做过滤
        orderItemAndVcsInfo = getOrderItemDOSVcs(bundleOrderCodeSet, orderItemAndVcsInfo);

        //商品快照获取
        Map<String, ProductSnapshotDTO> orderSkuItemMap = getProductSnapShotMap(orderItemAndVcsInfo.stream().map(OrderItemRefundVcsPo::getProductVersionCode).collect(Collectors.toList()));


        Map<String, String> decodeListText = piplDataUtil.getDecodeListText(carVinList);
        Map<String, String> map = redisService.getCacheMap(REDIS_KEY.SERIES_CACHE_KEY);
        List<OrderNewBrandRespVO> resultList = new ArrayList<>();
        for (OrderInfoDO orderInfoDO : orderInfoPage.getRecords()){
            List<OrderSkuItemVo> orderSkuItem = new ArrayList<>();
            BigDecimal totalAmount = BigDecimal.ZERO;
            int totalPoints = 0;
            for (OrderItemRefundVcsPo item : orderItemAndVcsInfo) {
                OrderSkuItemVo tmpOrderSkuItemVo = getSkuItemVo(orderInfoDO, item, couponDiscountItemMapByItemCode, parentOrderAndHisChild);
                if (tmpOrderSkuItemVo == null) continue;
                totalAmount = getTotalAmount(item, totalAmount);
                totalPoints = getTotalPoints(tmpOrderSkuItemVo, totalPoints);
                tmpOrderSkuItemVo.setUsedCouponCount(buildUsedCouponCount(item.getOrderItemCode(), couponOrderItemMapByItemCode));
                List<OrderRefundStatusMapDTO> orderRefundItemDOs = refundOrderInfoMap.get(item.getOrderItemCode());

                setBackCouponCount(orderRefundItemDOs, tmpOrderSkuItemVo);

                setServiceDate(tmpOrderSkuItemVo, item, couponOrderItemMapByItemCode);

                setAfterSalesStatus(item, tmpOrderSkuItemVo);

                setBusinessCode(orderInfoDO, item, allOrderCodeListIncludeParent, tmpOrderSkuItemVo);

                orderSkuItem.add(tmpOrderSkuItemVo);

                ProductSnapshotDTO productSnapshotDTO = orderSkuItemMap.get(item.getOrderItemCode());
                setCategory(productSnapshotDTO, tmpOrderSkuItemVo);
            }
            OrderNewBrandRespVO orderNewBrandRespVO = new OrderNewBrandRespVO();
            String orderCode = orderInfoDO.getOrderCode();

            orderNewBrandRespVO.setProductList(orderSkuItem);

            //订单信息载体
            OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();
            Integer orderStatus;

            // 如果是VSC商品，才组装车型信息
            if (Objects.equals(orderInfoDO.getBusinessCode(), BusinessIdEnum.VCS.getCode())){
                //如果是VCS订单，则orderItemBaseVOList肯定有且只有一条数据，所以直接取即可
                OrderBrandVehicleInfoVO vehicleInfo = new OrderBrandVehicleInfoVO();
                OrderItemRefundVcsPo orderItemRefundVcsPo = orderItemAndVcsInfo.stream().filter(x->x.getOrderCode().equals(orderInfoDO.getOrderCode())).findAny().orElse(null);
                if (orderItemRefundVcsPo == null){
                    log.warn("orderCode = {} 对应的vcsOrderInfoDO为空", orderCode);
                    return result;
                }
                setPayDownStatus(orderInfoDO, orderInfo);
                //2.组装车型信息 VCS才有
                String carVin = decodeListText.get(orderItemRefundVcsPo.getCarVin());
                vehicleInfo.setCarVin(carVin);
                vehicleInfo.setSeriesCode(orderItemRefundVcsPo.getSeriesCode());
                vehicleInfo.setSeriesName(handleSeriesName(map,orderItemRefundVcsPo.getSeriesCode(),orderItemRefundVcsPo.getSeriesName()));
                orderNewBrandRespVO.setVehicleInfo(vehicleInfo);

                //映射小程序状态 VCS保持原有逻辑
                orderInfo.setOrderStatus(orderInfoDO.getOrderStatus());
                List<OrderRefundStatusMapDTO> orderRefundStatusMapDTOS = refundOrderInfoMap.get(orderItemRefundVcsPo.getOrderItemCode());
                OrderRefundStatusMapDTO orderRefundStatusMapDTO = getOrderRefundStatusMapDTO(orderRefundStatusMapDTOS);
                orderStatus = getOrderStatus(getRefundOrderStatus(orderRefundStatusMapDTO), orderStatusMappingMap, orderInfo);
                orderInfo.setOrderCode(orderCode);
                orderInfo.setOrderType(orderInfoDO.getOrderType());

                // 供前端去筛选是否显示 查看发票 开发票功能
                orderInfo.setOrderStatus(orderStatus);
                assembleInvoiceStatusAndCostUpdated(invoiceStatusVOMap, orderInfo, orderItemRefundVcsPo);

                orderInfo.setOrderTime(orderInfoDO.getOrderTime());
                // 组合商品订单不展示服务时间
                setServiceDateVCS(orderInfoDO, orderInfo, orderItemRefundVcsPo);
                orderInfo.setOrderChannel(orderInfoDO.getOrderChannel());
                orderNewBrandRespVO.setOrderInfo(orderInfo);
                orderNewBrandRespVO.setOrderCode(orderCode);
                orderNewBrandRespVO.setOrderTime(orderInfoDO.getOrderTime());

                orderNewBrandRespVO.setBusinessCode(orderInfoDO.getBusinessCode());
                //4.组装订单标签信息
                orderNewBrandRespVO.setTagInfo(assembleTagInfoForVcs(orderInfoDO, orderItemRefundVcsPo.getOrderItemCode(), orderItemRefundVcsPo.getVcsRefundStatus() == null, feedbackRecordsMap.get(orderCode),feedbackConfigDOList));
            }else{
                //映射小程序状态
                Map<Integer, String> statusMap = buildNotVcsOrderStatus(OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType()), orderInfoDO.getCouponStatus(), orderInfoDO.getLogisticsStatus());
                //statusMap的key作为状态码，value作为状态描述

                orderInfo.setOrderStatus(statusMap.keySet().iterator().next());
                orderInfo.setCustomerOrderStatusView(statusMap.get(orderInfo.getOrderStatus()));
                orderInfo.setOrderCode(orderCode);
                orderInfo.setOrderType(orderInfoDO.getOrderType());
                orderInfo.setCostAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getCostAmount())));
                orderInfo.setCostPoints(orderInfoDO.getPointAmount());
                orderInfo.setDiscountAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getDiscountTotalAmount())));
                setCostAmountWithShipping(orderInfoDO, orderInfo);
                // 供前端去筛选是否显示 查看发票 开发票功能
                setInvoiceStatus(invoiceStatusVOMap, orderInfo);
                //设置订单行总价
                orderNewBrandRespVO.setTotalPoints(totalPoints);
                orderNewBrandRespVO.setTotalAmount(totalAmount);
                orderNewBrandRespVO.setOrderInfo(orderInfo);
                orderNewBrandRespVO.setBusinessCode(orderInfoDO.getBusinessCode());

            }
            orderNewBrandRespVO.setOrderCode(orderCode);
            resultList.add(orderNewBrandRespVO);
        }

        result.setList(resultList);
        result.setTotal(orderInfoPage.getTotal());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishOrder(String orderCode) {
        log.info("开始手动完成订单, orderCode={}", orderCode);
        // 查询订单当前状态，只有已支付才能修改为完成状态
        OrderInfoDO orderInfoDO = orderInfoDOMapper.queryOrderDoByOrderCode(orderCode);
        if (Objects.isNull(orderInfoDO)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        if (!OrderStatusEnum.PAID.getCode().equals(orderInfoDO.getOrderStatus())) {
            log.error("该订单不是已支付状态, 无法完成订单, orderCode={}", orderCode);
            throw exception(ErrorCodeConstants.ORDER_CANNOT_MANUAL_COMPLETED);
        }
        // 校验是否是非VCS商品
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType())
                || OrderTypeEnum.BRAND_GOOD.getCode().equals(orderInfoDO.getOrderType())
                || OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderInfoDO.getOrderType())) {
            log.error("该订单不是VCS订单, 无法完成订单, orderCode={}", orderCode);
            throw exception(ErrorCodeConstants.ORDER_CANNOT_MANUAL_COMPLETED);
        }
        // 查询服务状态是否存在激活失败, 必须存在服务激活状态为失败的才允许完成订单
        CommonResult<Boolean> result = vcsOrderFulfilmentApi.checkStatus(orderCode);
        if (Objects.isNull(result) || result.isError() || Boolean.FALSE.equals(result.getData())) {
            log.error("该订单没有激活失败的服务, 无法完成订单, orderCode={}", orderCode);
            throw exception(ErrorCodeConstants.ORDER_CANNOT_MANUAL_COMPLETED);
        }
        // 订单完成流程
        Integer beforeStatus = orderInfoDO.getOrderStatus();
        Integer event = OrderEventEnum.EVENT_TSDP_CALLBACK.getCode();

        //状态机去变更状态处理逻辑
        orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, null);

        //记录订单状态变化
        OrderStatusLogDO orderStatusLog = assembleOrderStatusLogDO(orderInfoDO.getOrderCode(),
                beforeStatus, orderInfoDO.getOrderStatus());
        // 更新订单信息
        orderInfoDO.setCompletedTime(LocalDateTime.now());
        orderInfoDO.setUpdatedTime(LocalDateTime.now());
        orderInfoDOMapper.updateById(orderInfoDO);

        // 插入订单状态日志
        orderStatusLogDOMapper.insert(orderStatusLog);

        // 记录操作日志
        modifyDetailLogMapper.createModifyLog(orderInfoDO.getOrderCode(), OrderModifyLogEnum.MANUAL_FINISH_ORDER.getDescription(),
                null, null);

        log.info("完成手动完成订单, orderCode={}", orderCode);
        return true;
    }

    @Override
    public Boolean getFailAlert() {
        CommonResult<Set<String>> result = null;
        try {
            result = vcsOrderFulfilmentApi.getFailOrders();
        } catch (Exception e) {
            log.error("查询激活失败的订单失败", e);
        }
        if (Objects.isNull(result) || result.isError()) {
            throw exception(ErrorCodeConstants.SUBSCRIPTION_SERVICE_ERROR);
        }
        Set<String> orderCodeList = result.getData();
        // 不存在激活失败的订单
        if (CollUtil.isEmpty(orderCodeList)) {
            return false;
        }
        // 查询订单状态是否为已支付
        LambdaQueryWrapper<OrderInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderInfoDO::getOrderCode, orderCodeList)
                .eq(OrderInfoDO::getOrderStatus, OrderStatusEnum.PAID.getCode())
                .eq(OrderInfoDO::getIsDeleted, false);
        List<String> paidOrderCodeList = orderInfoDOMapper.selectList(wrapper)
                .stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
        // 已支付且服务激活失败的订单
        log.info("已支付且服务激活失败的订单, orderCodeList={}", paidOrderCodeList);
        return CollUtil.isNotEmpty(paidOrderCodeList);
    }

    private static void setCostAmountWithShipping(OrderInfoDO orderInfoDO, OrderBrandOrderInfoVO orderInfo) {
        if(orderInfoDO.getFreightAmount() != null) {
            orderInfo.setShipping(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getFreightAmount())));
            orderInfo.setCostAmountIncludeShipping(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getFreightAmount() + orderInfoDO.getCostAmount())));
        }else{
            orderInfo.setCostAmountIncludeShipping(orderInfo.getCostAmount());
        }
    }

    @Nullable
    private static OrderSkuItemVo getSkuItemVo(OrderInfoDO orderInfoDO, OrderItemRefundVcsPo item, Map<String, List<OrderDiscountDetailDO>> couponDiscountItemMapByItemCode, Map<String, List<String>> parentOrderAndHisChild) {
        OrderSkuItemVo tmpOrderSkuItemVo = getOrderSkuItemVo(orderInfoDO, item, couponDiscountItemMapByItemCode, parentOrderAndHisChild);
        if (tmpOrderSkuItemVo == null) return null;
        return tmpOrderSkuItemVo;
    }

    private static void setCategory(ProductSnapshotDTO productSnapshotDTO, OrderSkuItemVo tmpOrderSkuItemVo) {
        if (productSnapshotDTO != null){
            tmpOrderSkuItemVo.setCategoryCodeLevel1Name(productSnapshotDTO.getCategoryCodeLevel1Name());
            tmpOrderSkuItemVo.setCategoryCodeLevel2Name(productSnapshotDTO.getCategoryCodeLevel2Name());
            tmpOrderSkuItemVo.setCategoryCodeLevel3Name(productSnapshotDTO.getCategoryCodeLevel3Name());
        }
    }

    @Nullable
    private static OrderRefundStatusMapDTO getOrderRefundStatusMapDTO(List<OrderRefundStatusMapDTO> orderRefundStatusMapDTOS) {
        OrderRefundStatusMapDTO orderRefundStatusMapDTO = null;
        if (CollUtil.isNotEmpty(orderRefundStatusMapDTOS)){
            //VCS原来取的是 MAX（ID）那条
            orderRefundStatusMapDTO = orderRefundStatusMapDTOS.get(orderRefundStatusMapDTOS.size() - 1);
        }
        return orderRefundStatusMapDTO;
    }

    private static void setAfterSalesStatus(OrderItemRefundVcsPo item, OrderSkuItemVo tmpOrderSkuItemVo) {
        if(item != null){
            OrderItemAftersalesStatusEnum aftersalesStatus = OrderItemAftersalesStatusEnum.getByCode(item.getAftersalesStatus());
            tmpOrderSkuItemVo.setAftersalesStatus(aftersalesStatus == null ? null : aftersalesStatus.getCode());
            tmpOrderSkuItemVo.setAftersalesStatusDesc(aftersalesStatus == null ? null : aftersalesStatus.getName());
        }
    }

    private static void setBusinessCode(OrderInfoDO orderInfoDO, OrderItemRefundVcsPo item, List<OrderInfoDO> allOrderCodeListIncludeParent, OrderSkuItemVo tmpOrderSkuItemVo) {
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType())){
            //如果聚合父订单，则要去订单行实际所属的businesscode
            OrderInfoDO realBelongToOrderInfo = allOrderCodeListIncludeParent.stream().filter(x -> x.getOrderCode().equals(item.getOrderCode())).findFirst().orElse(null);
            if (realBelongToOrderInfo != null
                    && (BusinessIdEnum.BRAND_GOODS.getCode().equals(realBelongToOrderInfo.getBusinessCode())
                        || BusinessIdEnum.LRE.getCode().equals(realBelongToOrderInfo.getBusinessCode()))){
                tmpOrderSkuItemVo.setBusinessCode(realBelongToOrderInfo.getBusinessCode());
            }
        }else if(BusinessIdEnum.BRAND_GOODS.getCode().equals(orderInfoDO.getBusinessCode())
                || BusinessIdEnum.LRE.getCode().equals(orderInfoDO.getBusinessCode())){
            tmpOrderSkuItemVo.setBusinessCode(orderInfoDO.getBusinessCode());
        }
    }

    private static void putParentCodeAndCodeList(List<OrderInfoDO> allOrderCodeListIncludeParent, List<String> orderCodeList, Map<String, List<String>> parentOrderAndHisChild) {
        if (CollUtil.isNotEmpty(allOrderCodeListIncludeParent)) {
            for (OrderInfoDO orderInfoDO : allOrderCodeListIncludeParent) {
                if (!orderCodeList.contains(orderInfoDO.getOrderCode())){//去重
                    orderCodeList.add(orderInfoDO.getOrderCode());
                }

                if (parentOrderAndHisChild.containsKey(orderInfoDO.getParentOrderCode())) {
                    parentOrderAndHisChild.get(orderInfoDO.getParentOrderCode()).add(orderInfoDO.getOrderCode());
                } else {
                    List<String> childOrderCodeList = new ArrayList<>();
                    childOrderCodeList.add(orderInfoDO.getOrderCode());
                    parentOrderAndHisChild.put(orderInfoDO.getParentOrderCode(), childOrderCodeList);
                }
            }
        }
    }

    @NotNull
    private Map<String, List<OrderDiscountDetailDO>> getCouponDiscountItemMapByItemCode(List<String> orderCodeList) {
        Map<String, List<OrderDiscountDetailDO>> couponDiscountItemMapByItemCode = new HashMap<>();
        List<OrderDiscountDetailDO> orderDiscountDetailDOS = orderDiscountDetailDOMapper.selectList(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .in(OrderDiscountDetailDO::getOrderItemCode, orderCodeList)
                .eq(OrderDiscountDetailDO::getIsDeleted, 0));
        if (!CollUtil.isEmpty(orderDiscountDetailDOS)) {
            couponDiscountItemMapByItemCode = orderDiscountDetailDOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailDO::getOrderItemCode));
        }
        return couponDiscountItemMapByItemCode;
    }

    private void setBaseCatInfo(String consumerCode, List<OrderInfoDO> orderInfoDOList, List<OrderItemRefundVcsPo> orderItemAndVcsInfo) {
        List<String> vcsCodeList = orderInfoDOList.stream().filter(x -> BusinessIdEnum.VCS.getCode().equals(x.getBusinessCode())).map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
        Map<String, VCSOrderInfoDO> vcsOrderInfoDOMap;
        if (CollUtil.isNotEmpty(vcsCodeList)){
            List<VCSOrderInfoDO> vcsOrderInfoDOList= vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                    .eq(VCSOrderInfoDO::getConsumerCode, consumerCode)
                    .in(VCSOrderInfoDO::getOrderCode, vcsCodeList)
                    .eq(VCSOrderInfoDO::getIsDeleted, 0));
            vcsOrderInfoDOMap = vcsOrderInfoDOList.stream().collect(Collectors.toMap(VCSOrderInfoDO::getOrderCode, Function.identity(),(o, n) -> o));
            setCarInfo(orderItemAndVcsInfo, vcsOrderInfoDOMap);
        }
    }

    @NotNull
    private Map<String, List<OrderCouponDetailDO>> getCouponOrderItemMapByItemCode(List<String> orderCodeList) {
        Map<String, List<OrderCouponDetailDO>> couponOrderItemMapByItemCode = new HashMap<>();
        List<OrderCouponDetailDO> orderCouponDetailDOS = orderCouponDetailDOMapper.selectList(new LambdaQueryWrapper<OrderCouponDetailDO>()
                .in(OrderCouponDetailDO::getOrderCode, orderCodeList)
                .eq(OrderCouponDetailDO::getIsDeleted, 0));
        if (!CollUtil.isEmpty(orderCouponDetailDOS)) {
            couponOrderItemMapByItemCode = orderCouponDetailDOS.stream().collect(Collectors.groupingBy(OrderCouponDetailDO::getOrderItemCode));
        }
        return couponOrderItemMapByItemCode;
    }

    private static BigDecimal getTotalAmount(OrderItemRefundVcsPo item, BigDecimal totalAmount) {
        if (item.getProductSalePrice() != null) {
            totalAmount = totalAmount.add(BigDecimal.valueOf(item.getProductSalePrice()));
        }
        return totalAmount;
    }

    private static int getTotalPoints(OrderSkuItemVo tmpOrderSkuItemVo, int totalPoints) {
        if (tmpOrderSkuItemVo.getSalePoints() != null) {
            totalPoints += tmpOrderSkuItemVo.getSalePoints();
        }
        return totalPoints;
    }

    @Nullable
    private static OrderSkuItemVo getOrderSkuItemVo(OrderInfoDO orderInfoDO, OrderItemRefundVcsPo item,
                                                    Map<String, List<OrderDiscountDetailDO>> couponDiscountItemMapByItemCode,
                                                    Map<String, List<String>> parentOrderAndHisChild) {

        // Check if the order codes don't match
        if (!orderInfoDO.getOrderCode().equals(item.getOrderCode())) {
            // If business code is not null, return null
            if (orderInfoDO.getBusinessCode() != null) {
                return null;
            }

            // Check if the current order is a child of the item's order
            List<String> childOrderCodes = parentOrderAndHisChild.get(orderInfoDO.getOrderCode());
            if (CollUtil.isEmpty(childOrderCodes) || !childOrderCodes.contains(item.getOrderCode())) {
                return null;
            }
        }
        //1.组装商品信息
        OrderSkuItemVo tmpOrderSkuItemVo = new OrderSkuItemVo();
        tmpOrderSkuItemVo.setOrderItemCode(item.getOrderItemCode());
        tmpOrderSkuItemVo.setProductVersionCode(item.getProductVersionCode());
        tmpOrderSkuItemVo.setProductCode(item.getProductCode());
        tmpOrderSkuItemVo.setProductSkuCode(item.getProductSkuCode());
        tmpOrderSkuItemVo.setProductName(item.getProductName());
        tmpOrderSkuItemVo.setProductImageUrl(item.getProductImageUrl());
        List<OrderDiscountDetailDO> productCouponDetailDOList = couponDiscountItemMapByItemCode.get(item.getProductSkuCode());
        if(!CollUtil.isEmpty(productCouponDetailDOList)){
            tmpOrderSkuItemVo.setSalePoints(productCouponDetailDOList.stream().mapToInt(OrderDiscountDetailDO::getCostPoints).sum());
        }
        tmpOrderSkuItemVo.setSalePointsPrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(item.getCostAmount())));
        tmpOrderSkuItemVo.setProductAttribute(AttributeUtil.formatProductAttributes(item.getProductAttribute()));
        if (tmpOrderSkuItemVo.getProductMarketPrice() != null) {
            tmpOrderSkuItemVo.setProductMarketPrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(item.getProductMarketPrice())));
        }
        tmpOrderSkuItemVo.setProductSalePrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(item.getProductSalePrice())));
        tmpOrderSkuItemVo.setProductQuantity(item.getProductQuantity());
        tmpOrderSkuItemVo.setItemFufilementType(item.getItemFufilementType());
        return tmpOrderSkuItemVo;
    }

    private static void setBackCouponCount(List<OrderRefundStatusMapDTO> orderRefundItemDOs, OrderSkuItemVo tmpOrderSkuItemVo) {
        if (CollUtil.isEmpty(orderRefundItemDOs)){
          return;
        }
        if (ProductFulfilmentTypeEnum.PHYSICAL_DELIVERY.getCode().equals(tmpOrderSkuItemVo.getItemFufilementType())){
            //BG的话从成功的第一条去退回数量
            OrderRefundStatusMapDTO orderRefundStatusMapDTO = orderRefundItemDOs.stream().filter(x -> REFUND_COMPLETED.getCode() == x.getRefundOrderStatus()).findFirst().orElse(null);
            tmpOrderSkuItemVo.setBackCouponCount(orderRefundStatusMapDTO == null ? 0 : orderRefundStatusMapDTO.getRefundQuantity());
        }else{
            //VCS和LRE保持原逻辑，取最后一条
            OrderRefundStatusMapDTO orderRefundItemDO = orderRefundItemDOs.get(orderRefundItemDOs.size() - 1);
            tmpOrderSkuItemVo.setBackCouponCount(orderRefundItemDO.getRefundQuantity() == null ? 0 : orderRefundItemDO.getRefundQuantity());
        }
    }

    @Nullable
    private static Integer getRefundOrderStatus(OrderRefundStatusMapDTO orderRefundStatusMapDTO) {
        return orderRefundStatusMapDTO == null ? null : orderRefundStatusMapDTO.getRefundOrderStatus();
    }

    private static void setServiceDateVCS(OrderInfoDO orderInfoDO, OrderBrandOrderInfoVO orderInfo, OrderItemRefundVcsPo orderItemRefundVcsPo) {
        if (!OrderTypeEnum.BUNDLED_GOODS.getCode().equals(orderInfoDO.getOrderType())) {
            orderInfo.setServiceBeginDate(orderItemRefundVcsPo.getServiceBeginDate());
            orderInfo.setServiceEndDate(orderItemRefundVcsPo.getServiceEndDate());
        }
    }

    private static void setInvoiceStatus(Map<String, InvoiceStatusVO> invoiceStatusVOMap, OrderBrandOrderInfoVO orderInfo) {
        if(invoiceStatusVOMap != null){
            InvoiceStatusVO invoiceStatusVO = invoiceStatusVOMap.get(orderInfo.getOrderCode());
            if(invoiceStatusVO != null){
                orderInfo.setInvoiceStatus(invoiceStatusVO.getInvoiceStatus());
            }
        }
    }

    private void setServiceDate(OrderSkuItemVo tmpOrderSkuItemVo, OrderItemRefundVcsPo item, Map<String, List<OrderCouponDetailDO>> couponOrderItemMapByItemCode) {
        if (!CollUtil.isEmpty(couponOrderItemMapByItemCode.get(item.getOrderItemCode()))) {
            LocalDateTime startTime = couponOrderItemMapByItemCode.get(item.getOrderItemCode()).get(0).getValidStartTime();
            LocalDateTime endTime = couponOrderItemMapByItemCode.get(item.getOrderItemCode()).get(0).getValidEndTime();
            if (startTime != null) {
                item.setServiceBeginDate(startTime);
            }
            if (endTime != null) {
                item.setServiceEndDate(endTime);
            }
            tmpOrderSkuItemVo.setCouponValidityStr(buildValidityDesc(item.getServiceBeginDate(), item.getServiceEndDate()));
        }
    }

    private static void setCarInfo(List<OrderItemRefundVcsPo> orderItemAndVcsInfo, Map<String, VCSOrderInfoDO> vcsOrderInfoDOMap) {
        for (OrderItemRefundVcsPo item : orderItemAndVcsInfo){
            VCSOrderInfoDO tmpVcsOrder = vcsOrderInfoDOMap.get(item.getOrderCode());
            if (tmpVcsOrder != null){
                setCatInfoNotNull(item, tmpVcsOrder);
            }
        }
    }

    private static void setCatInfoNotNull(OrderItemRefundVcsPo item, VCSOrderInfoDO tmpVcsOrder) {
        if (StringUtils.isEmpty(item.getCarVin())){
            item.setCarVin(tmpVcsOrder.getCarVin());
        }
        if (StringUtils.isEmpty(item.getSeriesCode())){
            item.setSeriesCode(tmpVcsOrder.getSeriesCode());
        }

        if (StringUtils.isEmpty(item.getSeriesName())) {
            item.setSeriesName(tmpVcsOrder.getSeriesName());
        }
        if (item.getServiceBeginDate() == null) {
            item.setServiceBeginDate(tmpVcsOrder.getServiceBeginDate());
        }
        if (item.getServiceEndDate() == null) {
            item.setServiceEndDate(tmpVcsOrder.getServiceEndDate());
        }
    }

    /***
     * <AUTHOR>
     * @description 为非VCS商品构建订单状态
     * 如果没有传 couponStatus 和 logisticsStatus 会异常
     * @param isParentOrder: 是否为聚合父订单
     * @param couponStatus: 优惠券状态
     * @param logisticsStatus: 物流订单状态
     * @return: java.util.Map<java.lang.Integer,java.lang.String>, Map中只会返回一个键值对，key会状态值，value为状态描述
    */

    private Map<Integer, String> buildNotVcsOrderStatus(Boolean isParentOrder, Integer couponStatus, Integer logisticsStatus){
        Map<Integer, String> result = new HashMap<>();
        //如果是父订单（拆分前的单子，一定是待支付.如果订单未支付就超时关闭了，也不会进入这个路基）
        if (Boolean.TRUE.equals(isParentOrder)){
            result.put(OrderLogisticsStatusEnum.PENDING_PAYMENT.getCode(), OrderLogisticsStatusEnum.PENDING_PAYMENT.getName());//如果是父订单，固定返回1-待支付
            return result;
        }
        String statusText;
        int status;
        //couponStatus和logisticsStatus 有且只能一个有值
        if (couponStatus != null){
            status = couponStatus;
            if (OrderCouponStatusEnum.VERIFIED.getCode() == status){//已核销要展示为订单完成
                statusText = OrderStatusEnum.COMPLETED.getDescription();
            }else{
                statusText = OrderCouponStatusEnum.getByCodeStrict(status).getName();
            }
        }else if (logisticsStatus != null){
            status = logisticsStatus;
            statusText = OrderLogisticsStatusEnum.getByCodeStrict(status).getName();
        }else {
            log.info("couponStatus 和 logisticsStatus同时为空，暂不支持");
            throw new IllegalArgumentException("内部异常");
        }
        result.put(status,statusText);
        return result;

    }
    private void handleList(List<OrderInfoPageVO> list, List<OrderInfoPageVO> result) {
//        Map<String, String> decodeContactText = getDecodedTextMap(list, OrderInfoPageVO::getContactPhoneCT, Constants.AUTH_CODE_VIEW_PHONE);
//        Map<String, String> decodeIcrText = getDecodedTextMap(list, OrderInfoPageVO::getIcrCT, Constants.AUTH_CODE_VIEW_ICR);
//        Map<String, String> decodeCarVinText = getDecodedTextMap(list, OrderInfoPageVO::getCarVinCT, Constants.AUTH_CODE_VIEW_VIN);

        for (OrderInfoPageVO vo : list) {
            vo.setStatusText(OrderStatusLogEnum.getDescriptionByCode(vo.getAfterStatus()));
//            vo.setContactPhone(Optional.ofNullable(decodeContactText).map(map -> map.get(vo.getContactPhoneCT())).orElse(vo.getContactPhone()));
//            vo.setInControlId(Optional.ofNullable(decodeIcrText).map(map -> map.get(vo.getIcrCT())).orElse(vo.getInControlId()));
//            vo.setCarVin(Optional.ofNullable(decodeCarVinText).map(map -> map.get(vo.getCarVinCT())).orElse(vo.getCarVin()));
            result.add(vo);
        }
    }

    private Map<String, String> getDecodedTextMap(List<OrderInfoPageVO> list, Function<OrderInfoPageVO, String> mapper, String authCode) {
        if (securityFrameworkService.hasPermission(authCode)) {
            List<String> values = list.stream().map(mapper).distinct().collect(Collectors.toList());
            return piplDataUtil.getDecodeListText(values);
        }
        return Collections.emptyMap();
    }


    @Override
    public OrderDetailRespVO getOrderDetail(String orderCode) {
        OrderDetailRespVO orderDetailRespVO = new OrderDetailRespVO();
        orderDetailRespVO.setOrderCode(orderCode);
        //查询OrderCode是否存在
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapper<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, 0));
        if (orderInfoDO == null) {
            return orderDetailRespVO;
        }
        //组装OrderDetailEInvoiceInfoVO 电子普票信息
        OrderDetailInvoiceInfoVO invoiceInfo = assembleInvoiceInfoVO(orderCode);
        orderDetailRespVO.setEInvoiceInfo(invoiceInfo.getEInvoiceInfo());

        //组装OrderDetailPaperInvoiceInfoVO 纸质专票信息
        orderDetailRespVO.setPaperInvoiceInfo(invoiceInfo.getPaperInvoiceInfo());
        if (orderInfoDO.getBusinessCode().equals(BusinessIdEnum.VCS.getCode())){
            //组装OrderDetailOrderStatusVO 订单状态
            OrderDetailOrderStatusVO orderStatusVO = assembleOrderStatusVO(orderCode);
            orderDetailRespVO.setOrderStatusInfo(orderStatusVO);
            //组装OrderDetailOrderInfoVO 订单信息
            OrderDetailOrderInfoVO orderInfoVO = assembleOrderInfoVO(orderInfoDO);
            orderDetailRespVO.setOrderInfo(orderInfoVO);
            //组装赠品信息
            assembleGiftInfoVo(orderInfoDO, orderDetailRespVO);
            //sprint47 组装评价信息
            assembleFeedInfoVO(orderInfoDO, orderDetailRespVO);
            //组装商品和车辆信息 商品信息
            assembleProductAndVehicleInfoVO(orderInfoDO, orderDetailRespVO);
        }

        if (orderInfoDO.getBusinessCode().equals(BusinessIdEnum.LRE.getCode())){
            // 订单信息
            OrderDetailOrderInfoVO orderInfoVO = assembleEcouponOrderInfoVO(orderInfoDO);
            orderDetailRespVO.setOrderInfo(orderInfoVO);
            // 支付信息
            assemblePaymentInfoVO(orderInfoDO, orderDetailRespVO);
            // 电子专票信息
            orderDetailRespVO.setESpecialInvoiceInfo(invoiceInfo.getESpecialInvoiceInfo());
            // 商品信息
            assembleLreProductInfoVO(orderInfoDO, orderDetailRespVO);
        }

        if (orderInfoDO.getBusinessCode().equals(BusinessIdEnum.BRAND_GOODS.getCode())){
            // 订单信息
            OrderDetailOrderInfoVO orderInfoVO = assembleLogisticsOrderInfoVO(orderInfoDO);
            
            // 支付信息
            assemblePaymentInfoVO(orderInfoDO, orderDetailRespVO);
            // 电子专票信息
            orderDetailRespVO.setESpecialInvoiceInfo(invoiceInfo.getESpecialInvoiceInfo());
            // 商品信息
            assembleBgProductInfoVO(orderInfoDO, orderDetailRespVO);

            // 订单状态 - 售后中映射 处理
            // productInfo 的 BrandGoodsProductItemInfoList 中的 item 的  RefundOrderStatus 为 1-4 的，orderInfoVO 的 orderStatus 需要映射为 售后处理中（90501）
            if (orderDetailRespVO.getProductInfo() != null && 
                orderDetailRespVO.getProductInfo().getBrandGoodsProductItemInfoList() != null) {
                orderDetailRespVO.getProductInfo().getBrandGoodsProductItemInfoList().forEach(item -> {
                    if (item.getRefundOrderStatus() != null && 
                        item.getRefundOrderStatus() >= 1 && item.getRefundOrderStatus() <= 4) {
                        orderInfoVO.setDisplayOrderStatus(90501);
                    }
                });
            }
            orderDetailRespVO.setOrderInfo(orderInfoVO);

        }
        //组装OrderDetailConsumerInfoVO 客户信息
        OrderDetailCustomerInfoVO customerInfoVO = assembleCustomerInfoVO(orderInfoDO);
        orderDetailRespVO.setCustomerInfo(customerInfoVO);


        return orderDetailRespVO;
    }

    /**
     * 组装订单详细信息视图对象。
     *
     * @param orderInfoDO 订单信息
     * @return OrderDetailOrderInfoVO 订单详细信息视图对象
     */
    private OrderDetailOrderInfoVO assembleEcouponOrderInfoVO(OrderInfoDO orderInfoDO) {
        // 创建OrderDetailOrderInfoVO对象并设置属性
        OrderDetailOrderInfoVO orderInfoVO = new OrderDetailOrderInfoVO();
        // 订单状态 枚举值和描述
        orderInfoVO.setOrderStatus(orderInfoDO.getCouponStatus());
        // 支付状态 枚举值和描述
        orderInfoVO.setPaymentStatus(orderInfoDO.getPaymentStatus());
        orderInfoVO.setPaymentStatusDesc(PaymentStatusEnum.getDescriptionByCode(orderInfoDO.getPaymentStatus()));
        // 商品履约方式（履约类型）
        orderInfoVO.setOrderType(orderInfoDO.getOrderType());
        // 主订单号
        orderInfoVO.setParentOrderCode(orderInfoDO.getParentOrderCode());
        // 子订单号
        orderInfoVO.setOrderCode(orderInfoDO.getOrderCode());
        // 订单创建时间
        orderInfoVO.setOrderCreateTime(TimeFormatUtil.timeToStringByFormat(orderInfoDO.getOrderTime(), TimeFormatUtil.formatter_6));
        // 订单完成时间
        orderInfoVO.setOrderCompleteTime(TimeFormatUtil.timeToStringByFormat(orderInfoDO.getCompletedTime(), TimeFormatUtil.formatter_6));
        // 客户留言
        orderInfoVO.setCustomerRemark(orderInfoDO.getCustomerRemark());
        // 运营人员备注
        orderInfoVO.setOperatorRemark(orderInfoDO.getOperatorRemark());
        return orderInfoVO;
    }

    /**
     * 组装bg订单详细信息视图对象。
     *
     * @param orderInfoDO 订单信息
     * @return OrderDetailOrderInfoVO 订单详细信息视图对象
     */
    private OrderDetailOrderInfoVO assembleLogisticsOrderInfoVO(OrderInfoDO orderInfoDO) {
        OrderDetailOrderInfoVO orderInfoVO = new OrderDetailOrderInfoVO();
        // 创建OrderDetailOrderInfoVO对象并设置属性
        orderInfoVO.setOrderStatus(orderInfoDO.getLogisticsStatus());
        orderInfoVO.setDisplayOrderStatus(orderInfoDO.getLogisticsStatus());
        // 支付状态 枚举值和描述
        orderInfoVO.setPaymentStatus(orderInfoDO.getPaymentStatus());
        orderInfoVO.setPaymentStatusDesc(PaymentStatusEnum.getDescriptionByCode(orderInfoDO.getPaymentStatus()));
        // 商品履约方式（履约类型）
        orderInfoVO.setOrderType(orderInfoDO.getOrderType());
        // 主订单号
        orderInfoVO.setParentOrderCode(orderInfoDO.getParentOrderCode());
        // 子订单号
        orderInfoVO.setOrderCode(orderInfoDO.getOrderCode());
        // 订单创建时间
        orderInfoVO.setOrderCreateTime(TimeFormatUtil.timeToStringByFormat(orderInfoDO.getOrderTime(), TimeFormatUtil.formatter_6));
        // 订单完成时间
        orderInfoVO.setOrderCompleteTime(TimeFormatUtil.timeToStringByFormat(orderInfoDO.getCompletedTime(), TimeFormatUtil.formatter_6));
        // 客户留言
        orderInfoVO.setCustomerRemark(orderInfoDO.getCustomerRemark());
        // 运营人员备注
        orderInfoVO.setOperatorRemark(orderInfoDO.getOperatorRemark());
        return orderInfoVO;
    }


    private void assemblePaymentInfoVO(OrderInfoDO orderInfoDO, OrderDetailRespVO orderDetailRespVO) {
        List<OrderDiscountDetailDO> orderDiscountDetailDOList = orderDiscountDetailDOMapper.selectList(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderDiscountDetailDO::getIsDeleted, false));

        OrderDiscountDetailDO orderDiscountDetailDOByCoupon = orderDiscountDetailDOList.stream().filter(item -> item.getDiscountType().equals(DiscountTypeEnum.COUPON_DISCOUNT.getType())).findFirst().orElse(null);

        OrderPaymentRecordsDO paymentRecord = orderPaymentRecordsMapper.queryLastByOrderCode(orderInfoDO.getOrderCode());

        List<String> paymentMethodList = new ArrayList<>();
        OrderDetailPaymentInfoVO paymentInfoVO = new OrderDetailPaymentInfoVO();
        paymentInfoVO.setCostAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getCostAmount() + orderInfoDO.getFreightAmount())));
        if (paymentInfoVO.getCostAmount() != null) {
            paymentMethodList.add(PayTypeEnum.CASH.getDesc());
        }
        paymentInfoVO.setPointAmount(orderInfoDO.getPointAmount());
        paymentInfoVO.setFreightAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getFreightAmount())));
        if (paymentInfoVO.getPointAmount() != null && paymentInfoVO.getPointAmount() > 0) {
            paymentMethodList.add(PayTypeEnum.POINTS.getDesc());
        }
        if (orderDiscountDetailDOByCoupon != null && orderDiscountDetailDOByCoupon.getCouponCode() != null) {
            paymentInfoVO.setCouponType(CouponTypeEnum.getByType(orderDiscountDetailDOByCoupon.getCouponModelClassify()).getDesc());
            paymentInfoVO.setCouponName(orderDiscountDetailDOByCoupon.getCouponModelName());
            paymentInfoVO.setCouponCode(orderDiscountDetailDOByCoupon.getCouponCode());
        }
        if (paymentInfoVO.getCouponType() != null) {
            paymentMethodList.add(PayTypeEnum.COUPON.getDesc());
        }
        paymentInfoVO.setPaymentMethod(paymentMethodList.stream().collect(Collectors.joining("+")));
        if (paymentRecord != null && paymentRecord.getPayFinishTime() !=null) {
            paymentInfoVO.setPaymentTime(TimeFormatUtil.timeToStringByFormat(paymentRecord.getPayFinishTime(), TimeFormatUtil.formatter_6));
        }
        orderDetailRespVO.setPaymentInfo(paymentInfoVO);
    }

    /**
     * 获取订单评价信息
     *
     * @param orderInfoDO
     * @param orderDetailRespVO
     */
    private void assembleFeedInfoVO(OrderInfoDO orderInfoDO, OrderDetailRespVO orderDetailRespVO) {
        assembleFeedbackInfoVO(orderInfoDO, orderDetailRespVO);
    }

    /**
     * 公共方法：组装订单-评价信息视图
     *
     * @param orderInfoDO 订单号
     * @param target 目标响应对象，可以是 OrderDetailRespVO 或 OrderAppDetailPage
     */
    private void assembleFeedbackInfoVO(OrderInfoDO orderInfoDO, Object target) {
        List<OrderFeedbackInfo> feedbackInfos = new ArrayList<>();

        List<String> orders = new ArrayList<>();
        orders.add(orderInfoDO.getOrderCode());
        orders.add(orderInfoDO.getParentOrderCode());

        // 查询反馈记录
        List<FeedbackRecordsDO> feedbackRecords = feedbackRecordsDOMapper.selectList(new LambdaQueryWrapper<FeedbackRecordsDO>()
                .in(FeedbackRecordsDO::getOrderCode, orders)
                .eq(FeedbackRecordsDO::getIsDeleted, 0));

        if (CollUtil.isEmpty(feedbackRecords)) {
            log.info("订单-评价信息为空, orderCode={}", orders);
            return;
        }

        // 解析反馈记录
        feedbackInfos = feedbackRecords.stream().map(FeedbackParserUtil::parseFeedbackRecord).collect(Collectors.toList());

        // 设置反馈信息到目标对象
        if (target instanceof OrderDetailRespVO) {
            ((OrderDetailRespVO) target).setFeedbackInfos(feedbackInfos);
        } else if (target instanceof OrderAppDetailPage) {
            ((OrderAppDetailPage) target).setFeedbackInfos(feedbackInfos);
        }
    }

    /**
     * 组装退单信息
     *
     * @param itemInfoVO
     * @param orderInfo
     */
    private void setRefundInfo(ProductBrandCategoriedItemInfoVO itemInfoVO, OrderInfoDO orderInfo) {
        // 查询退单信息
        OrderRefundDO orderRefundDO = orderRefundDOMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundDO::getOriginOrderCode, orderInfo.getOrderCode())
                .orderByDesc(OrderRefundDO::getId)
                .select(OrderRefundDO::getId, OrderRefundDO::getRefundOrderCode, OrderRefundDO::getRejectReason)
                .last(Constants.LIMIT_ONE));
        if (orderRefundDO == null) {
            return;
        }
        itemInfoVO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
        itemInfoVO.setRejectReason(orderRefundDO.getRejectReason());
        // 查询退单明细
        List<OrderRefundItemDO> refundItemDOS = orderRefundItemDOMapper.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundItemDO::getRefundOrderCode, orderRefundDO.getRefundOrderCode()));
        // key为orderItemCode value为结束时间
        Map<String, LocalDateTime> itemCodeMapEndDate = refundItemDOS.stream()
                .filter(item -> item.getServiceEndDate() != null)
                .collect(Collectors.toMap(OrderRefundItemDO::getOrderItemCode, OrderRefundItemDO::getServiceEndDate, (o, n) -> n));
        if (CollUtil.isEmpty(itemCodeMapEndDate)) {
            return;
        }
        // 普通商品
        if (OrderItemSpuTypeEnum.NORMAL_GOOD.getCode().equals(itemInfoVO.getOrderItemSpuType())) {
            if (itemInfoVO.getServiceEndDate() != null && OrderStatusEnum.isCancelled(orderInfo.getOrderStatus())) {
                itemInfoVO.setActualEndDate(itemCodeMapEndDate.get(itemInfoVO.getOrderItemCode()));
            }
            return;
        }
        // 组合商品
        for (ProductBrandCategoriedItemInfoVO infoVO : itemInfoVO.getNext()) {
            if (infoVO.getServiceEndDate() != null && OrderStatusEnum.isCancelled(orderInfo.getOrderStatus())) {
                infoVO.setActualEndDate(itemCodeMapEndDate.get(infoVO.getOrderItemCode()));
            }
        }
    }

/*    private void setRefundInfo(ProductItemInfoVO itemInfoVO, OrderInfoDO orderInfo) {
        OrderRefundItemDO orderRefundItemDO = orderRefundItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderRefundItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundItemDO::getOrderItemCode, itemInfoVO.getOrderItemCode())
                .orderByDesc(OrderRefundItemDO::getId)
                .select(OrderRefundItemDO::getId, OrderRefundItemDO::getRefundOrderCode)
                .last(Constants.LIMIT_ONE));
        if (orderRefundItemDO != null) {
            OrderRefundDO orderRefundDO = orderRefundDOMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                    .eq(BaseDO::getIsDeleted, false)
                    .eq(OrderRefundDO::getRefundOrderCode, orderRefundItemDO.getRefundOrderCode())
                    .orderByDesc(OrderRefundDO::getId)
                    .select(OrderRefundDO::getId, OrderRefundDO::getRefundOrderCode, OrderRefundDO::getServiceEndDate, OrderRefundDO::getRejectReason)
                    .last(Constants.LIMIT_ONE));
            if (orderRefundDO != null) {
                itemInfoVO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
                itemInfoVO.setRejectReason(orderRefundDO.getRejectReason());

                if (itemInfoVO.getServiceEndDate() != null) {
                    if (OrderStatusEnum.isCancelled(orderInfo.getOrderStatus())) {
                        itemInfoVO.setActualEndDate(orderRefundDO.getServiceEndDate());
                    }
                }

            }
        }
    }*/

    /**
     * 编辑订单
     *
     * @param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editOrderDetail(OrderEditDTO orderEditDTO) {
        log.info("后台编辑订单时，入参：[editOrderDetail][orderEditDTO({})]", orderEditDTO);
        // 参数校验
        RequestValidationUtil.checkPaperInvoiceDTO(orderEditDTO.getPaperInvoiceInfo(), orderEditDTO.isViewInvoice());
        RequestValidationUtil.checkESpecialInvoiceDTO(orderEditDTO.getESpecialInvoiceDTO(), orderEditDTO.isViewInvoice());


        // 先通过orderCode 去t_order_info表中查询是否存在该订单
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapper<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderEditDTO.getOrderCode())
                .eq(OrderInfoDO::getIsDeleted, 0));
        if (Objects.isNull(orderInfoDO)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }

        // 1.通过orderCode 和 operatorRemark 更新t_order_info表中的operator_remark字段
        boolean updateOrderInfoSuccess = false;

        String oldOperatorRemark = orderInfoDO.getOperatorRemark();
        if (!Objects.equals(oldOperatorRemark, orderEditDTO.getOperatorRemark())) {
            log.info("[editOrderDetail][orderCode({}) 更新 operatorRemark({}) -> {}]", orderEditDTO.getOrderCode(), oldOperatorRemark, orderEditDTO.getOperatorRemark());
            updateOrderInfoSuccess = true;
            orderInfoDO.setOperatorRemark(orderEditDTO.getOperatorRemark());
            orderInfoDOMapper.updateById(orderInfoDO);
        }

        boolean updateInvoiceSuccess = false;
        // 2.RPC调用payment服务修改纸质发票记录
        PaperInvoiceModifyRespDTO updateInvoiceResp = updateInvoiceInfo(orderEditDTO);
        if (updateInvoiceResp != null) {
            updateInvoiceSuccess = updateInvoiceResp.getSuccess();
        }

        updateLreBGESpecialInvoiceInfo(orderEditDTO, orderInfoDO);

        // 3.通过商品itemList, productItemInfoList中的 ProductItemInfoVO中的 orderCode 和 productSkuCode更新t_order_item表中的remark字段，批量入库t_order_item表
        boolean updateOrderItemsSuccess = false;

        String orderCode = orderEditDTO.getOrderCode();
        Map<String, String> productSkuCodeAndRemarkMap = new HashMap<>();
        List<OrderEditItemDTO> productItemInfoList = orderEditDTO.getProductItemInfoList();

        buildProductSkuCodeAndRemarkMap(productItemInfoList, productSkuCodeAndRemarkMap);

        //拿orderCode 和 nonEmptySkuCodes 去t_order_item表中查询；并比对 remark是否相同，相同则不更新，不同则更新
        Triple<Boolean, JSONObject, JSONObject> triple = updateOrderItems(productSkuCodeAndRemarkMap, orderCode, updateOrderItemsSuccess);
        updateOrderItemsSuccess = triple.getLeft();

        if (updateOrderInfoSuccess) {
            String contentName;
            String remarkKey;
            if (BusinessIdEnum.LRE.getCode().equals(orderInfoDO.getBusinessCode()) || BusinessIdEnum.BRAND_GOODS.getCode().equals(orderInfoDO.getBusinessCode())) {
                contentName = OrderModifyLogEnum.EDIT_ORDER_INFO.getDescription();
                remarkKey = "运营人员备注";
            } else {
                contentName = OrderModifyLogEnum.EDIT_ORDER_REMARK.getDescription();
                remarkKey = "订单备注";
            }
            //记录订单操作日志 t_order_modify_log 订单编辑
            JSONObject oldVal = new JSONObject();
            JSONObject newVal = new JSONObject();
            oldVal.put(remarkKey, oldOperatorRemark == null ? "" : oldOperatorRemark);
            newVal.put(remarkKey, orderEditDTO.getOperatorRemark() == null ? "" : orderEditDTO.getOperatorRemark());
            modifyDetailLogMapper.createModifyLog(orderInfoDO.getOrderCode(), contentName,
                    oldVal.toJSONString(), newVal.toJSONString());
        }
        if (updateInvoiceSuccess) {
            //记录发票操作日志 t_order_modify_log 发票编辑
            modifyDetailLogMapper.createModifyLog(orderInfoDO.getOrderCode(), OrderModifyLogEnum.EDIT_INVOICE_INFO.getDescription(),
                    updateInvoiceResp.getOldVal(), updateInvoiceResp.getNewVal());

        }

        if (updateOrderItemsSuccess) {
            //记录订单操作日志 t_order_modify_log 商品编辑
            modifyDetailLogMapper.createModifyLog(orderInfoDO.getOrderCode(), OrderModifyLogEnum.EDIT_PRODUCT_REMARK.getDescription(),
                    triple.getMiddle().toJSONString(), triple.getRight().toJSONString());
        }

        return true;
    }

    private void updateLreBGESpecialInvoiceInfo(OrderEditDTO orderEditDTO, OrderInfoDO orderInfoDO) {
        if (BusinessIdEnum.LRE.getCode().equals(orderInfoDO.getBusinessCode()) || BusinessIdEnum.BRAND_GOODS.getCode().equals(orderInfoDO.getBusinessCode())) {
            boolean updateESpecialInvoiceSuccess = false;
            // 2.RPC调用payment服务修改纸质发票记录 LRE / BG
            ESpecialInvoiceModifyRespDTO updateESpecialInvoiceResp = updateESpecialInvoiceInfo(orderEditDTO);
            if (updateESpecialInvoiceResp != null) {
                updateESpecialInvoiceSuccess = updateESpecialInvoiceResp.getSuccess();
            }

            if (updateESpecialInvoiceSuccess) {
                //记录电子专票操作日志 t_order_modify_log 电子专票编辑
                modifyDetailLogMapper.createModifyLog(orderInfoDO.getOrderCode(), OrderModifyLogEnum.EDIT_ESPECIAL_INVOICE_INFO.getDescription(),
                        updateESpecialInvoiceResp.getOldVal(), updateESpecialInvoiceResp.getNewVal());
            }
        }
    }

    /**
     * 组装商品备注集合
     */
    private static void buildProductSkuCodeAndRemarkMap(List<OrderEditItemDTO> productItemInfoList, Map<String, String> productSkuCodeAndRemarkMap) {
        if (CollUtil.isNotEmpty(productItemInfoList)) {
            // 过滤掉 getProductSkuCode 为空的项
            List<OrderEditItemDTO> nonEmptySkuCodes = productItemInfoList.stream()
                    .filter(item -> item.getProductSkuCode() != null)
                    .collect(Collectors.toList());

            // 获取productItemInfoList中 将productSkuCode作为key remark作为value的map
            if (CollUtil.isNotEmpty(nonEmptySkuCodes)) {
                for (OrderEditItemDTO item : nonEmptySkuCodes) {
                    String skuCode = item.getProductSkuCode();
                    if (skuCode != null) {
                        productSkuCodeAndRemarkMap.put(skuCode, item.getRemark());
                    }
                }
            }
        }
    }

    private Triple<Boolean, JSONObject, JSONObject> updateOrderItems(Map<String, String> productSkuCodeAndRemarkMap, String orderCode, boolean updateOrderItemsSuccess) {
        List<OrderItemDO> orderItemDOS = null;
        if (CollUtil.isNotEmpty(productSkuCodeAndRemarkMap)) {
            orderItemDOS = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                    .eq(OrderItemDO::getOrderCode, orderCode)
                    .in(OrderItemDO::getProductSkuCode, productSkuCodeAndRemarkMap.keySet())
                    .eq(OrderItemDO::getIsDeleted, 0)
            );
        }
        JSONObject oldJson = new JSONObject();
        JSONObject newJson = new JSONObject();

        if (CollUtil.isNotEmpty(orderItemDOS)) {
            for (OrderItemDO orderItemDO : orderItemDOS) {
                String oldRemark = orderItemDO.getRemark();
                String newRemark = productSkuCodeAndRemarkMap.get(orderItemDO.getProductSkuCode());
                if (!StrUtil.equals(oldRemark, newRemark)) {
                    updateOrderItemsSuccess = true;
                    orderItemDO.setRemark(newRemark);
                    orderItemDOMapper.updateById(orderItemDO);
                    oldJson.put(orderItemDO.getProductName() + ":商品备注", oldRemark == null ? "" : oldRemark);
                    newJson.put(orderItemDO.getProductName() + ":商品备注", newRemark == null ? "" : newRemark);
                }
            }
        }
        return new ImmutableTriple<>(updateOrderItemsSuccess, oldJson, newJson);
    }

    /**
     * 修改纸质发票信息
     *
     * @param orderEditDTO orderEditDTO
     */
    private PaperInvoiceModifyRespDTO updateInvoiceInfo(OrderEditDTO orderEditDTO) {
        PaperInvoiceDTO paperInvoiceDTO = orderEditDTO.getPaperInvoiceInfo();
        if (Objects.isNull(paperInvoiceDTO)) {
            return null;
        }
        PaperInvoiceModifyReqDTO modifyReqDTO = new PaperInvoiceModifyReqDTO();
        BeanUtil.copyProperties(paperInvoiceDTO, modifyReqDTO);
        modifyReqDTO.setOrderNo(orderEditDTO.getOrderCode());
        // 如果有查看权限，则代表传的明文，不做处理；没有查看权限，则传的是密文，需要转成明文
        if (!orderEditDTO.isViewInvoice()) {
            // PIPL字段处理
            buildRecipientInfo(modifyReqDTO);
        }
        CommonResult<PaperInvoiceModifyRespDTO> modifyResult = new CommonResult<>();
        try {
            modifyResult = invoiceApi.modifyPaperInvoice(modifyReqDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (Objects.isNull(modifyResult.getCode()) || modifyResult.getCode() != 0 || Objects.isNull(modifyResult.getData())) {
            return null;
        }
        return modifyResult.getData();
    }

    /**
     * 修改电子专票信息
     *
     * @param orderEditDTO orderEditDTO
     */
    private ESpecialInvoiceModifyRespDTO updateESpecialInvoiceInfo(OrderEditDTO orderEditDTO) {
        ESpecialInvoiceDTO eSpecialInvoiceDTO = orderEditDTO.getESpecialInvoiceDTO();
        if (Objects.isNull(eSpecialInvoiceDTO)) {
            return null;
        }
        ESpecialInvoiceModifyReqDTO eSpecialInvoiceModifyReqDTO = new ESpecialInvoiceModifyReqDTO();
        BeanUtil.copyProperties(eSpecialInvoiceDTO, eSpecialInvoiceModifyReqDTO);
        eSpecialInvoiceModifyReqDTO.setOrderNo(orderEditDTO.getOrderCode());
        // 如果有查看权限，则代表传的明文，不做处理；没有查看权限，则传的是密文，需要转成明文
        if (!orderEditDTO.isViewInvoice()) {
            // PIPL字段处理
            buildESpecialInvoiceRecipientInfo(eSpecialInvoiceModifyReqDTO);
        }
        CommonResult<ESpecialInvoiceModifyRespDTO> modifyResult = new CommonResult<>();
        try {
            modifyResult = invoiceApi.modifyESpecialInvoice(eSpecialInvoiceModifyReqDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (Objects.isNull(modifyResult.getCode()) || modifyResult.getCode() != 0 || Objects.isNull(modifyResult.getData())) {
            return null;
        }
        return modifyResult.getData();
    }

    /**
     * 组装接收人信息
     */
    private void buildRecipientInfo(PaperInvoiceModifyReqDTO modifyReqDTO) {
        if (Objects.nonNull(modifyReqDTO.getRecipientName())) {
            modifyReqDTO.setRecipientName(piplDataUtil.getDecodeText(modifyReqDTO.getRecipientName()));
        }
        if (Objects.nonNull(modifyReqDTO.getRecipientPhone())) {
            modifyReqDTO.setRecipientPhone(piplDataUtil.getDecodeText(modifyReqDTO.getRecipientPhone()));
        }
        if (Objects.nonNull(modifyReqDTO.getRecipientAddress())) {
            modifyReqDTO.setRecipientAddress(piplDataUtil.getDecodeText(modifyReqDTO.getRecipientAddress()));
        }
    }

    /**
     * 组装电子专票接收人信息
     */
    private void buildESpecialInvoiceRecipientInfo(ESpecialInvoiceModifyReqDTO modifyReqDTO) {
        if (Objects.nonNull(modifyReqDTO.getRecipientName())) {
            modifyReqDTO.setRecipientName(piplDataUtil.getDecodeText(modifyReqDTO.getRecipientName()));
        }
        if (Objects.nonNull(modifyReqDTO.getRecipientPhone())) {
            modifyReqDTO.setRecipientPhone(piplDataUtil.getDecodeText(modifyReqDTO.getRecipientPhone()));
        }
        if (Objects.nonNull(modifyReqDTO.getRecipientAddress())) {
            modifyReqDTO.setRecipientAddress(piplDataUtil.getDecodeText(modifyReqDTO.getRecipientAddress()));
        }
    }


    //TODO 改造点4 优化查询
    private OrderStatusMappingDO getOrderStatusMapping(Integer orderStatus, Integer refundOrderStatus) {
        // 还未退款
        if (refundOrderStatus == null) {
            refundOrderStatus = -1;
        }

        return orderStatusMappingDOMapper.getStatusMapping(orderStatus, refundOrderStatus);
    }

    @Override
    public PageResult<OrderModifyPageVO> selectModifyPage(OrderModifyPageReqDTO dto) {
        PageResult<OrderModifyDetailLogDO> pageResult = modifyDetailLogMapper.selectPage(dto, new LambdaQueryWrapperX<OrderModifyDetailLogDO>()
                .eq(OrderModifyDetailLogDO::getOrderCode, dto.getOrderCode())
                .orderByDesc(OrderModifyDetailLogDO::getCreatedTime));
        List<OrderModifyPageVO> list = pageResult.getList().stream().map(item -> {
            OrderModifyPageVO vo = new OrderModifyPageVO();
            BeanUtils.copyProperties(item, vo);
            String oldValue = item.getModifyFieldOldValue();
            Integer fieldCount = item.getModifyFieldCount();
            vo.setModifyFieldCount(fieldCount);
            if (Objects.nonNull(oldValue)) {
                Set<String> oldSet = JSON.parseObject(oldValue, Feature.OrderedField).keySet();
                vo.setModifyField(String.join("、", oldSet));
            }
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public OrderModifyDetailVO getLogDetail(Long id) {
        OrderModifyDetailLogDO result = modifyDetailLogMapper.selectOne(new LambdaQueryWrapperX<OrderModifyDetailLogDO>()
                .eq(OrderModifyDetailLogDO::getId, id));
        if (Objects.isNull(result)) {
            return null;
        }
        OrderModifyDetailVO detailVO = new OrderModifyDetailVO();
        BeanUtil.copyProperties(result, detailVO);
        return detailVO;
    }

    @Override
    public OrderAppDetailPage getAppOrderDetail(String orderCode, String jlrId) {
        OrderAppDetailPage orderAppDetailPage = new OrderAppDetailPage();
        //查询OrderCode是否存在
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapper<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, 0));
        if (orderInfoDO == null) {
            log.info("订单不存在:{}", orderCode);
            throw ServiceExceptionUtil.exception(ORDER_NOT_EXISTS);
        }

        // 判断订单是否属于该用户
        if (!orderInfoDO.getConsumerCode().equals(jlrId)) {
            log.info("订单不属于该用户, orderCode={}, consumerCode={}, jlrId={}", orderCode, orderInfoDO.getConsumerCode(), jlrId);
            return orderAppDetailPage;
        }
        //1. 组装单个商品信息
        ProductBrandCategoriedItemInfoVO itemInfoVO;

        // 根据订单号查询所有相关的商品项
        List<OrderItemDO> orderItems = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getIsDeleted, 0));
        // 检查是否查询到商品项
        if (CollUtil.isEmpty(orderItems)) {
            return orderAppDetailPage;
        }

        OrderItemDO bundleItem = orderItems.stream().filter(item -> OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode().equals(item.getOrderItemSpuType())).findFirst().orElse(null);
        String orderItemCode;
        List<String> orderItemCodes;
        // 商品项存在组合商品 一定是VCS商品
        if (bundleItem != null) {
            orderItemCode = bundleItem.getOrderItemCode();
            //a.设置捆绑商品基本信息 t_order_item表能查到的信息
            itemInfoVO = assembleProductBrandCategoriedItemInfo(bundleItem);
            // 设置组合商品信息
            List<ProductBrandCategoriedItemInfoVO> childItemList = orderItems.stream().filter(item -> OrderItemSpuTypeEnum.NORMAL_GOOD.getCode().equals(item.getOrderItemSpuType()))
                    .map(this::assembleProductBrandCategoriedItemInfo)
                    .collect(Collectors.toList());
            orderItemCodes = childItemList.stream().map(ProductBrandCategoriedItemInfoVO::getOrderItemCode).collect(Collectors.toList());
            itemInfoVO.setNext(childItemList);
        } else {
            // 不存在默认只有一个商品项
            OrderItemDO orderItemDO = orderItems.get(0);
            orderItemCode = orderItemDO.getOrderItemCode();
            //a.设置商品基本信息 t_order_item表能查到的信息
            itemInfoVO = assembleProductBrandCategoriedItemInfo(orderItemDO);
            orderItemCodes = List.of(orderItemCode);
        }

        //2. 组装车型信息
        buildCarInfoVO(itemInfoVO, orderAppDetailPage, orderItemCodes);

        //3. 组装订单信息
        buildOrderInfoVO(orderAppDetailPage, orderInfoDO, orderItemCode);

        //4. 组装赠品信息
        buildGiftInfoVO(orderAppDetailPage, orderInfoDO);

        //5. 组装订单-评价信息
        buildFeedbackInfoVO(orderAppDetailPage, orderInfoDO);

        return orderAppDetailPage;
    }

    @Override
    public OrderAppDetailPage getAppOrderDetail2(String orderCode, String jlrId) {
        OrderAppDetailPage orderAppDetailPage = new OrderAppDetailPage();
        orderAppDetailPage.setOrderInfo(new OrderBrandOrderInfoVO());
        //实际支付金额(总价）
        BigDecimal payAmount = BigDecimal.ZERO;
        //商品原售价(总价）
        BigDecimal originalPrice = BigDecimal.ZERO;


        //查询OrderCode是否存在 (注意该订单是不是一个聚合父订单)
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapper<OrderInfoDO>()
                .eq(OrderInfoDO::getConsumerCode, jlrId)
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, 0));
        if (orderInfoDO == null) {
            log.info("订单orderCode={}不存在,或不属于当前用户{} ", orderCode, jlrId);
            return orderAppDetailPage;
        }

        boolean isVcsOrder = BusinessIdEnum.VCS.getCode().equals(orderInfoDO.getBusinessCode());

        if (isVcsOrder) {
            log.info("暂不兼容VCS商品详情查看");
            throw ServiceExceptionUtil.exception(ORDER_NOT_SUPPORT_VCS);
        }
        //要确认当前订单是否为聚合父订单，如果是，则OrderItem的返回要变大
        //1. 组装单个商品信息
        List<ProductBrandCategoriedItemInfoVO> itemInfoVOs = new ArrayList<>();
        List<OrderItemDO> orderItems;
        List<String> orderItemCodes;

        boolean isParentOrder = false; //如果这是一个父订单
        Map<String, OrderInfoDO> orderInfoDOMap = new HashMap<>();//如果当前单是父订单，则orderInDo实体可能有多个.
        Map<String, List<OrderRefundItemDO>> orderRefundItemDOMap = new HashMap<>();
        Map<String, List<OrderRefundDO>> orderRefundDoMap = new HashMap<>();
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType())) {
            isParentOrder = true;
            List<OrderInfoDO> orderInfoDOS = getOrderInfoDOS(orderCode, jlrId);
            orderInfoDOMap = orderInfoDOS.stream().collect(Collectors.toMap(OrderInfoDO::getOrderCode, Function.identity()));
            List<String> allOrderCodeList = orderInfoDOS.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
            orderItems = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                    .in(OrderItemDO::getOrderCode, allOrderCodeList)
                    .eq(OrderItemDO::getIsDeleted, 0)
                    .orderByAsc(OrderItemDO::getId));
            orderItemCodes = orderItems.stream().map(OrderItemDO::getOrderItemCode).collect(Collectors.toList());
        } else {
            orderInfoDOMap.put(orderCode, orderInfoDO);
            orderItems = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                    .eq(OrderItemDO::getOrderCode, orderCode)
                    .eq(OrderItemDO::getIsDeleted, 0)
                    .orderByAsc(OrderItemDO::getId));
            orderItemCodes = orderItems.stream().map(OrderItemDO::getOrderItemCode).collect(Collectors.toList());
            //退款订单行信息
            orderRefundItemDOMap = getOrderRefundItemDOMap(orderItemCodes, orderRefundItemDOMap);

            orderRefundDoMap = getOrderRefundDoMap(orderInfoDO, orderRefundDoMap);
        }

        // 检查是否查询到商品项
        if (CollUtil.isEmpty(orderItems)) {
            log.info("订单orderCode={}不存在订单行", orderCode);
            return orderAppDetailPage;
        }

        // 优惠券信息列表，主要用来组装是否核销/是否退还字段；如果是聚合父订单，这个为空，因为聚合父订单都是未支付状态，不用查。
        Map<String, List<OrderCouponDetailDO>> couponOrderItemMapByItemCode = new HashMap<>();
        // 非聚合父订单，只有履约模式是电子优惠券的才查这个（目前LRE商品就是电子商品）
        couponOrderItemMapByItemCode = getCouponOrderItemMapByItemCode(isParentOrder, orderInfoDO, orderItems, couponOrderItemMapByItemCode);

        //优惠分摊明细，主要为了查询该订单是否有使用优惠券的情况
        List<OrderDiscountDetailDO> orderDiscountDetailDOS = orderDiscountDetailDOMapper.selectList(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .in(OrderDiscountDetailDO::getOrderItemCode, orderItemCodes)
                .eq(OrderDiscountDetailDO::getIsDeleted, 0));

        List<OrderItemLogisticsDO> orderItemLogisticsDOS = orderItemLogisticsDOMapper.selectList(new LambdaQueryWrapper<OrderItemLogisticsDO>()
                .in(OrderItemLogisticsDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderItemLogisticsDO::getIsDeleted, false)
        );

        Map<String, OrderItemLogisticsDO> orderItemLogisticsDOSMap = getStringOrderItemLogisticsDOMap(orderItemLogisticsDOS);

        //商品快照分类信息
        Map<String, ProductSnapshotDTO> productSnapshotMap = getProductSnapShotMap(orderItems.stream().map(OrderItemDO::getProductVersionCode).collect(Collectors.toList()));

        BigDecimal invocableAmount = BigDecimal.ZERO;
        //对非VCS的商品进行订单行信息封装
        //if (!isVcsOrder) {
        for (OrderItemDO orderItemDO : orderItems) {
            OrderInfoDO realBelongToOrderInfoDo = orderInfoDO;
            if (orderInfoDO.getOrderType().equals(OrderTypeEnum.PARENT.getCode())){
                realBelongToOrderInfoDo = orderInfoDOMap.get(orderItemDO.getOrderCode());
            }


            //总的支付金额
            payAmount = payAmount.add(BigDecimal.valueOf(orderItemDO.getCostAmount()));

            originalPrice = originalPrice.add(BigDecimal.valueOf(orderItemDO.getProductSalePrice()).multiply(BigDecimal.valueOf(orderItemDO.getProductQuantity())));

            //组装订单行(商品行)数据
            ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO = assembleProductBrandCategoriedItemInfo(orderItemDO);
            productBrandCategoriedItemInfoVO.setProductSalePoints(orderItemDO.getPointAmount());
            productBrandCategoriedItemInfoVO.setProductImageUrl(orderItemDO.getProductImageUrl());
            productBrandCategoriedItemInfoVO.setUsedCouponCount(buildUsedCouponCount(orderItemDO.getOrderItemCode(), couponOrderItemMapByItemCode));
            productBrandCategoriedItemInfoVO.setSalePrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderItemDO.getProductSalePrice())));
            productBrandCategoriedItemInfoVO.setBusinessCode(realBelongToOrderInfoDo.getBusinessCode());


            setUnitPointsInfo(orderItemDO, productBrandCategoriedItemInfoVO);

            OrderDiscountDetailDO orderDiscountDetailDO = orderDiscountDetailDOS.stream().filter(x -> x.getOrderItemCode().equals(orderItemDO.getOrderItemCode())).findAny().orElse(null);
            setCouponPrice(orderDiscountDetailDO, productBrandCategoriedItemInfoVO);


            /**====================================计算可开票金额和可退款金额==========================================**/
            BigDecimal costAmount = BigDecimal.valueOf(orderItemDO.getCostAmount()); //实付价格
            //找出正在退款或者已经退款的金额
            List<OrderRefundItemDO> orderRefundItemDOList = orderRefundItemDOMap.get(orderItemDO.getOrderItemCode());//先找出关联的refund item项

            HasRefundAmountAndCount tmpRefundInfo = getHasRefundAmountAndCount(orderItemDO, orderRefundItemDOList, orderRefundDoMap);


            List<OrderCouponDetailDO> orderCouponDetailDOS = couponOrderItemMapByItemCode.get(orderItemDO.getOrderItemCode());
            int usedCouponCount = orderCouponDetailDOS == null ? 0 : (int) orderCouponDetailDOS.stream().filter(x -> Objects.equals(EcouponStatusEnum.VERIFIED.getCode(), x.getStatus()))
                    .count();

            //BigDecimal maxRefundAmount = costAmount.subtract(hasRefundedAmount).subtract(hasUsedCouponAmount);
            Integer maxRefundMoney =0;
            if (CartItemTypeEnum.BRAND_GOODS.getCode().equals(orderItemDO.getItemFulfillmentType())){
                maxRefundMoney = refundHandler.getLogisticsMaxRefundMoney(orderItemDO,null);
                productBrandCategoriedItemInfoVO.setMaxRefundSkuCount(orderItemDO.getProductQuantity() - tmpRefundInfo.hasRefundCount - usedCouponCount - tmpRefundInfo.refundingCount);
            }else if(CartItemTypeEnum.E_COUPON.getCode().equals(orderItemDO.getItemFulfillmentType())) {
                maxRefundMoney = refundHandler.getMaxRefundMoney(orderItemDO,RefundOrderOperationTypeEnum.SYSTEM_AUTO.getCode());
                productBrandCategoriedItemInfoVO.setMaxRefundSkuCount(orderItemDO.getProductQuantity() - tmpRefundInfo.hasRefundCount - usedCouponCount);
            }
            productBrandCategoriedItemInfoVO.setMaxRefundAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(maxRefundMoney)));
            //如果订单行本身有消耗积分,或者可退金额大于零，才会有可退积分
            setRefundPoints(orderItemDO, productBrandCategoriedItemInfoVO, maxRefundMoney);

            invocableAmount = invocableAmount.add(costAmount).subtract(tmpRefundInfo.hasRefundedAmount);


            /**====================================计算可开票金额，可退积分和可退款金额==========================================**/
            setRefundInfo(orderItemDO, orderRefundItemDOMap, productBrandCategoriedItemInfoVO, tmpRefundInfo);

            OrderItemLogisticsDO orderItemLogisticsDO = getOrderItemLogisticsDO(orderItemDO, orderItemLogisticsDOSMap, productBrandCategoriedItemInfoVO);

            //
            setCanShowCardStatus(orderItemDO, productBrandCategoriedItemInfoVO, orderInfoDO, tmpRefundInfo);

            //如果是电子兑换券，需要展示有效期
            setItemVoInfo(orderItemDO, couponOrderItemMapByItemCode, productBrandCategoriedItemInfoVO, orderInfoDO, orderItemLogisticsDO, productSnapshotMap);

            itemInfoVOs.add(productBrandCategoriedItemInfoVO);
        }
        buildNotVcsOrderInfoVO(orderAppDetailPage, orderInfoDO);

        setOrderInfoSecond(orderAppDetailPage, originalPrice, orderInfoDO, invocableAmount, orderDiscountDetailDOS);

        //如果找不到优惠分摊明细，则表示没有使用过优惠券
        if (orderInfoDO.getDiscountTotalAmount() != null ){
            orderAppDetailPage.getOrderInfo().setDiscountAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getDiscountTotalAmount() )));
        }


        orderAppDetailPage.setOrderItemList(itemInfoVOs);
        return orderAppDetailPage;
    }

    private static void setCanShowCardStatus(OrderItemDO orderItemDO, ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO, OrderInfoDO orderInfoDO, HasRefundAmountAndCount tmpRefundInfo) {
        productBrandCategoriedItemInfoVO.setCanShowCardStatus((OrderCouponStatusEnum.VERIFIED.getCode().equals(orderInfoDO.getCouponStatus())
                || OrderCouponStatusEnum.PENDING_VERIFICATION.getCode().equals(orderInfoDO.getCouponStatus())) && orderItemDO.getProductQuantity() > tmpRefundInfo.hasRefundCount);
    }

    @NotNull
    private static Map<String, OrderItemLogisticsDO> getStringOrderItemLogisticsDOMap(List<OrderItemLogisticsDO> orderItemLogisticsDOS) {
        //BG商品发货信息
        Map<String , OrderItemLogisticsDO> orderItemLogisticsDOSMap = new HashMap<>();
        if (CollUtil.isNotEmpty(orderItemLogisticsDOS)) {
            orderItemLogisticsDOSMap = orderItemLogisticsDOS.stream()
                    .collect(Collectors.toMap(OrderItemLogisticsDO::getOrderItemCode, Function.identity()));
        }
        return orderItemLogisticsDOSMap;
    }

    private static void setRefundInfo(OrderItemDO orderItemDO, Map<String, List<OrderRefundItemDO>> orderRefundItemDOMap, ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO, HasRefundAmountAndCount tmpRefundInfo) {
        List<OrderRefundItemDO> orderRefundItemDO = orderRefundItemDOMap.get(orderItemDO.getOrderItemCode());
        if (CollUtil.isNotEmpty(orderRefundItemDO)){
            OrderRefundItemDO lastOrderRefund = orderRefundItemDO.get(orderRefundItemDO.size() - 1);
            productBrandCategoriedItemInfoVO.setBackCouponCount(tmpRefundInfo.hasRefundCount);
            productBrandCategoriedItemInfoVO.setRefundOrderCode(lastOrderRefund.getRefundOrderCode());
        }
        if(orderRefundItemDO != null){
            OrderItemAftersalesStatusEnum aftersalesStatus = OrderItemAftersalesStatusEnum.getByCode(orderItemDO.getAftersalesStatus());
            productBrandCategoriedItemInfoVO.setAftersalesStatus(aftersalesStatus == null ? null : aftersalesStatus.getCode());
            productBrandCategoriedItemInfoVO.setAftersalesStatusDesc(aftersalesStatus == null ? null : aftersalesStatus.getName());
        }
    }

    @Nullable
    private static OrderItemLogisticsDO getOrderItemLogisticsDO(OrderItemDO orderItemDO, Map<String, OrderItemLogisticsDO> orderItemLogisticsDOSMap, ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO) {
        //如果有物流信息，需要封装
        OrderItemLogisticsDO orderItemLogisticsDO = orderItemLogisticsDOSMap.get(orderItemDO.getOrderItemCode());
        if (orderItemLogisticsDO != null){
            productBrandCategoriedItemInfoVO.setLogisticsCompany(orderItemLogisticsDO.getLogisticsCompany());
            productBrandCategoriedItemInfoVO.setLogisticsNo(orderItemLogisticsDO.getLogisticsNo());
            productBrandCategoriedItemInfoVO.setNoReasonReturnsTime(orderItemLogisticsDO.getNoReasonReturnsTime());
        }
        return orderItemLogisticsDO;
    }

    private Map<String, ProductSnapshotDTO> getProductSnapShotMap(List<String> orderItems) {
        Map<String, ProductSnapshotDTO> productSnapshotMap = new HashMap<>();
        Set<String> productSnapShotCodeList = new HashSet<>(orderItems);
        if (!CollUtil.isEmpty(productSnapShotCodeList)){
            CommonResult<List<ProductSnapshotDTO>> productSnapshotList = productSnapshotApi.getProductSnapshotList(productSnapShotCodeList);
            if (!productSnapshotList.isSuccess()){
                log.info("获取商品快照失败:入参:{},响应:{}", productSnapShotCodeList, productSnapshotList);
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.GET_SKU_SNAPSHOT_FAILED);
            }
            if (CollUtil.isNotEmpty(productSnapshotList.getData())){
                productSnapshotMap = productSnapshotList.getData().stream().collect(Collectors.toMap(ProductSnapshotDTO::getProductVersionCode, Function.identity()));
            }
        }
        return productSnapshotMap;
    }

    private static void setUnitPointsInfo(OrderItemDO orderItemDO, ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO) {
        if (orderItemDO.getPointAmount() != null && orderItemDO.getPointAmount() > 0) {//只有使用了积分赋值这两个字段
            //目前订单行的商品要么一起用积分，要么一起不用，而且商品的积分一定是一样，所以这里可以直接除。如果未来业务变成允许订单行内的商品不同时用积分，那么这里就要修改了。
            productBrandCategoriedItemInfoVO.setUnitSalePoints(orderItemDO.getPointAmount() / orderItemDO.getProductQuantity());
            productBrandCategoriedItemInfoVO.setUnitSalePointsPrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderItemDO.getCostAmount()/ orderItemDO.getProductQuantity())));
        }
    }

    private static void setCouponPrice(OrderDiscountDetailDO orderDiscountDetailDO, ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO) {
        if(orderDiscountDetailDO != null) {//如果没有分摊信息，可能没有使用优惠券，也可能是当前行不支持，不用赋值优惠单价
            BigDecimal unitCostPrice = MoneyUtil.convertToCents(productBrandCategoriedItemInfoVO.getCostAmount())//优惠单价取值逻辑：实际花费/数量，结果向下取整(分为单位)
                    .divide(BigDecimal.valueOf(productBrandCategoriedItemInfoVO.getProductQuantity()), 0, RoundingMode.DOWN);
            productBrandCategoriedItemInfoVO.setUnitCouponPrice(MoneyUtil.convertFromCents(unitCostPrice));
            productBrandCategoriedItemInfoVO.setCouponModuleName(orderDiscountDetailDO.getCouponModelName());

        }
    }

    @NotNull
    private static HasRefundAmountAndCount getHasRefundAmountAndCount(OrderItemDO orderItemDO, List<OrderRefundItemDO> orderRefundItemDOList, Map<String, List<OrderRefundDO>> orderRefundDoMap) {
        BigDecimal hasRefundedAmount = BigDecimal.ZERO;
        int hasRefundCount = 0;
        int refundingCount = 0;
        boolean foundCount = false;
        if (CollUtil.isEmpty(orderRefundItemDOList)){
            return new HasRefundAmountAndCount(hasRefundedAmount, hasRefundCount, refundingCount);
        }
        //在找出order item对应的refund Info项目
        List<OrderRefundDO> orderRefundDOList = orderRefundDoMap.get(orderItemDO.getOrderCode());
        //不管是BG商品还是LRE商品，都取第一个成功的退款的数量
        orderRefundItemDOList.sort(Comparator.comparing(OrderRefundItemDO::getCreatedTime));
        for (OrderRefundItemDO orderRefundItemDO: orderRefundItemDOList){
            InternalRefundInfo internalRefundInfo = new InternalRefundInfo(orderRefundItemDOList, hasRefundedAmount, hasRefundCount, refundingCount, foundCount, orderRefundDOList, orderRefundItemDO).invoke();
            hasRefundedAmount = internalRefundInfo.getHasRefundedAmount();
            hasRefundCount = internalRefundInfo.getHasRefundCount();
            refundingCount = internalRefundInfo.getRefundingCount();
            foundCount = internalRefundInfo.isFoundCount();
        }
        return new HasRefundAmountAndCount(hasRefundedAmount, hasRefundCount, refundingCount);
    }

    private static class HasRefundAmountAndCount {
        public final BigDecimal hasRefundedAmount;
        public final Integer hasRefundCount;
        public final Integer refundingCount;//退款中的数量，BG和LRE有点区别
        public HasRefundAmountAndCount(BigDecimal hasRefundedAmount, Integer hasRefundCount, Integer refundingCount) {
            this.hasRefundedAmount = hasRefundedAmount;
            this.hasRefundCount = hasRefundCount;
            this.refundingCount = refundingCount;
        }
    }

    @NotNull
    private List<OrderInfoDO> getOrderInfoDOS(String orderCode, String jlrId) {
        List<OrderInfoDO> orderInfoDOS = orderInfoDOMapper.selectList(new LambdaQueryWrapper<OrderInfoDO>()
                .eq(OrderInfoDO::getConsumerCode, jlrId)
                .eq(OrderInfoDO::getParentOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, 0));
        if (CollUtil.isEmpty(orderInfoDOS)) {
            log.info("订单orderCode={}不存在子订单,信息缺失", orderCode);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_INFO_LOST);
        }
        return orderInfoDOS;
    }

    private Map<String, List<OrderRefundItemDO>> getOrderRefundItemDOMap(List<String> orderItemCodes, Map<String, List<OrderRefundItemDO>> orderRefundItemDOMap) {
        List<OrderRefundItemDO> refundOrderItemInfoDOS = orderRefundItemDOMapper.selectList(new LambdaQueryWrapper<OrderRefundItemDO>()
                .in(OrderRefundItemDO::getOrderItemCode, orderItemCodes)
                .eq(OrderRefundItemDO::getIsDeleted, 0));
        if (CollUtil.isNotEmpty(refundOrderItemInfoDOS)) {
            orderRefundItemDOMap = refundOrderItemInfoDOS.stream().collect(Collectors.groupingBy(OrderRefundItemDO::getOrderItemCode));
        }
        return orderRefundItemDOMap;
    }

    private Map<String, List<OrderRefundDO>> getOrderRefundDoMap(OrderInfoDO orderInfoDO, Map<String, List<OrderRefundDO>> orderRefundDoMap) {
        List<OrderRefundDO> refundOrderInfoDOS = orderRefundDOMapper.selectList(new LambdaQueryWrapper<OrderRefundDO>()
                .eq(OrderRefundDO::getOriginOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderRefundDO::getIsDeleted, 0));
        if (CollUtil.isNotEmpty(refundOrderInfoDOS)) {
            orderRefundDoMap = refundOrderInfoDOS.stream().collect(Collectors.groupingBy(OrderRefundDO::getOriginOrderCode));
        }
        return orderRefundDoMap;
    }

    private void setRefundPoints(OrderItemDO orderItemDO, ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO, Integer maxRefundMoney) {
        if (needSetMaxRefund(productBrandCategoriedItemInfoVO, maxRefundMoney)){
            productBrandCategoriedItemInfoVO.setRefundSkuPoints(
            refundHandler.calculateRefundPoints(orderItemDO,
                    maxRefundMoney,
                    maxRefundMoney));
        }else{
            productBrandCategoriedItemInfoVO.setRefundSkuPoints(0);
        }
    }

    private static boolean needSetMaxRefund(ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO, Integer maxRefundMoney) {
        return productBrandCategoriedItemInfoVO.getProductSalePoints() != null || maxRefundMoney > 0;
    }

    private static void setOrderInfoSecond(OrderAppDetailPage orderAppDetailPage, BigDecimal originalPrice, OrderInfoDO orderInfoDO, BigDecimal invocableAmount, List<OrderDiscountDetailDO> orderDiscountDetailDOS) {
        orderAppDetailPage.getOrderInfo().setOriginalAmount(MoneyUtil.convertFromCents(originalPrice));
        orderAppDetailPage.getOrderInfo().setCostPoints(orderInfoDO.getPointAmount());
        if (orderInfoDO.getBusinessCode() != null && BusinessIdEnum.BRAND_GOODS.getCode().equals(orderInfoDO.getBusinessCode())){
            //如果是BG商品，可退票金额需要加上运费
            orderAppDetailPage.getOrderInfo().setInvocableAmount(MoneyUtil.convertFromCents(invocableAmount.add(BigDecimal.valueOf(orderInfoDO.getFreightAmount()))));
        }else{
            orderAppDetailPage.getOrderInfo().setInvocableAmount(MoneyUtil.convertFromCents(invocableAmount));
        }

        orderAppDetailPage.getOrderInfo().setCustomerRemark(orderInfoDO.getCustomerRemark());
        orderAppDetailPage.getOrderInfo().setOrderType(orderInfoDO.getOrderType());
        if(!CollUtil.isEmpty(orderDiscountDetailDOS)){
            if (orderDiscountDetailDOS.get(0).getCouponModelName() != null){//因为一个订单只能使用一个优惠券，所以取第一个就好。如果有模版名称，则一定不是积分，反之
                orderAppDetailPage.getOrderInfo().setCouponModuleName(orderDiscountDetailDOS.get(0).getCouponModelName());
            }else{
                orderAppDetailPage.getOrderInfo().setCouponUsed(true);
            }

        }
    }

    private void setItemVoInfo(OrderItemDO orderItemDO, Map<String, List<OrderCouponDetailDO>> couponOrderItemMapByItemCode,
                               ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO, OrderInfoDO orderInfoDO,
                               OrderItemLogisticsDO orderItemLogisticsDO, Map<String, ProductSnapshotDTO> productSnapshotMap) {
        //设置分类信息
        ProductSnapshotDTO productSnapshotDTO = productSnapshotMap.get(orderItemDO.getProductVersionCode());
        if (productSnapshotDTO != null){
            productBrandCategoriedItemInfoVO.setCategoryCodeLevel1Name(productSnapshotDTO.getCategoryCodeLevel1Name());
            productBrandCategoriedItemInfoVO.setCategoryCodeLevel2Name(productSnapshotDTO.getCategoryCodeLevel2Name());
            productBrandCategoriedItemInfoVO.setCategoryCodeLevel3Name(productSnapshotDTO.getCategoryCodeLevel3Name());
        }
        if (CartItemTypeEnum.E_COUPON.getCode().equals(orderItemDO.getItemFulfillmentType())){
            List<OrderCouponDetailDO> orderCouponDetailDOS = couponOrderItemMapByItemCode.get(orderItemDO.getOrderItemCode());
            if (!CollUtil.isEmpty(orderCouponDetailDOS)) {
                LocalDateTime startTime = orderCouponDetailDOS.get(0).getValidStartTime();
                LocalDateTime endTime = orderCouponDetailDOS.get(0).getValidEndTime();
                if (startTime != null) {
                    productBrandCategoriedItemInfoVO.setServiceBeginDate(startTime);
                }
                if (endTime != null) {
                    productBrandCategoriedItemInfoVO.setServiceEndDate(endTime);
                }
                productBrandCategoriedItemInfoVO.setCouponValidityStr(buildValidityDesc(productBrandCategoriedItemInfoVO.getServiceBeginDate(), productBrandCategoriedItemInfoVO.getServiceEndDate()));
            }

            //如果存在待核销的LRE商品，且没有正在售后或已完成售后的退款单就可以发起退款
            setCanRefundOrder(orderItemDO, productBrandCategoriedItemInfoVO, orderCouponDetailDOS, orderInfoDO);
            //电子兑换券（LRE）只有一个退款订单,BG商品退款单可以有多个，取最后一个
        }else if (CartItemTypeEnum.BRAND_GOODS.getCode().equals(orderItemDO.getItemFulfillmentType())){
            //待发货的商品可以申请售后
            //已发货/已收货/订单完成，则还需要满足，已发货时间25天以内
            setCanRefundOrderForBG(orderItemDO, productBrandCategoriedItemInfoVO, orderItemLogisticsDO, orderInfoDO);
            //添加订单行状态
            productBrandCategoriedItemInfoVO.setItemStatus(orderItemDO.getItemStatus());
        }
    }

    private static void setCanRefundOrder(OrderItemDO orderItemDO, ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO, List<OrderCouponDetailDO> orderCouponDetailDOS, OrderInfoDO orderInfoDO) {
        // 1. 如果当前是聚合父订单，直接不允许退货，因为聚合父订单能进入这里一定是待支付状态，待支付无法申请售后
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType())) {
            productBrandCategoriedItemInfoVO.setCanRefundOrder(false);
            return;
        }
        if (orderCouponDetailDOS != null && StringUtils.isEmpty(productBrandCategoriedItemInfoVO.getRefundOrderCode())){
            OrderCouponDetailDO orderCouponDetailDO = orderCouponDetailDOS.stream().filter(x -> x.getStatus().equals(EcouponStatusEnum.PENDING_USE.getCode())).findAny().orElse(null);
            //没有发生过售后 或者没有正在处理的售后单 才能显示售后按钮
            log.info("订单item编码={},订单item售后状态={}", orderItemDO.getOrderItemCode(), orderItemDO.getAftersalesStatus());
            if(!OrderItemAftersalesStatusEnum.PROCESSING.getCode().equals(orderItemDO.getAftersalesStatus()) && !OrderItemAftersalesStatusEnum.COMPLETED.getCode().equals(orderItemDO.getAftersalesStatus())){
                productBrandCategoriedItemInfoVO.setCanRefundOrder(orderCouponDetailDO != null);
            }
        }
    }

    private static void setCanRefundOrderForBG(OrderItemDO orderItemDO, ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO,
                                               OrderItemLogisticsDO orderItemLogisticsDO, OrderInfoDO orderInfoDO) {
        // 初始化默认为不可退货
        productBrandCategoriedItemInfoVO.setCanRefundOrder(false);

        // 1. 如果当前是聚合父订单，直接不允许退货，因为聚合父订单能进入这里一定是待支付状态，待支付无法申请售后
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType())) {
            productBrandCategoriedItemInfoVO.setCanRefundOrder(false);
            return;
        }

        //2. 如果有售后单或订单状态为待支付/已关闭，则不允许退货
        if (!StringUtils.isEmpty(productBrandCategoriedItemInfoVO.getRefundOrderCode())
                || OrderStatusEnum.ORDERED.getCode().equals(orderInfoDO.getOrderStatus())
                || OrderStatusEnum.CLOSED.getCode().equals(orderInfoDO.getOrderStatus())) {
            return;
        }

        // 3. 检查物流状态决定是否允许退货, 当前订单行没有被关闭才有可能申请售后
        if (!OrderItemLogisticsStatusEnum.CLOSED.getCode().equals(orderItemDO.getItemStatus())) {
            // 非关闭状态的商品项
            if (orderItemLogisticsDO.getSendTime() == null  //如果商品本身没有发货（待发货），但是用户又确认收货了（此时状态应该变成了已收货），也要能申请售后
                    || orderItemLogisticsDO.getSendTime().plusDays(25).isAfter(LocalDateTime.now())) {
                // 已发货且在25天内允许退货
                productBrandCategoriedItemInfoVO.setCanRefundOrder(true);
            }
        }
    }

    private Map<String, List<OrderCouponDetailDO>> getCouponOrderItemMapByItemCode(boolean isParentOrder, OrderInfoDO orderInfoDO, List<OrderItemDO> orderItems, Map<String, List<OrderCouponDetailDO>> couponOrderItemMapByItemCode) {
        List<OrderCouponDetailDO> couponOrderItemInfoDOS;
        if (!isParentOrder && OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderInfoDO.getOrderType())) {
            //通过订单号获取，
            couponOrderItemInfoDOS = orderCouponDetailDOMapper.selectList(new LambdaQueryWrapper<OrderCouponDetailDO>()
                    .in(OrderCouponDetailDO::getOrderItemCode, orderItems.stream().map(OrderItemDO::getOrderItemCode).collect(Collectors.toList()))
                    .eq(OrderCouponDetailDO::getIsDeleted, 0));
            if (!CollUtil.isEmpty(couponOrderItemInfoDOS)) {
                couponOrderItemMapByItemCode = couponOrderItemInfoDOS.stream().collect(Collectors.groupingBy(OrderCouponDetailDO::getOrderItemCode));
            }
        }
        return couponOrderItemMapByItemCode;
    }

    /***
     * <AUTHOR>
     * @description 生成电子券(LRE商品)状态描述
     * @date 2025/3/10 15:00
     * @param orderItemCode: 当前订单行的code
     * @param orderItemMap:  订单号与LRE订单商品列的Map表
     * @return: java.lang.String
    */
    private Integer buildUsedCouponCount(String orderItemCode, Map<String, List<OrderCouponDetailDO>> orderItemMap){
        if (orderItemMap == null){
            return null;
        }
        List<OrderCouponDetailDO> couponOrderItemInfoDOS = orderItemMap.get(orderItemCode);
        if (couponOrderItemInfoDOS == null){
            return null;
        }
        int verifiedCount = (int) couponOrderItemInfoDOS.stream().filter(x -> Objects.equals(EcouponStatusEnum.VERIFIED.getCode(), x.getStatus())).count();
        if (verifiedCount == 0){
            return null;
        }
        // 组装返回字符串
        return verifiedCount;
    }

    /***
     * <AUTHOR>
     * @description 生成电子券(LRE商品)有效期描述
     * @date 2025/3/10 15:00
     * @param startTime: 开始时间
     * @param endTime:  结束时间
     * @return: java.lang.String
     */
    private String buildValidityDesc(LocalDateTime startTime, LocalDateTime endTime) {
        StringBuilder validityDesc = new StringBuilder("有效期：");
        //同一个订单行的优惠券的有效期一定是一样的
        if (startTime != null) {
            validityDesc.append(startTime.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))).append("-");
        }
        //格式转成日期，不要时间部分
        if (endTime == null) {
            validityDesc = new StringBuilder(Constants.PERMANENT_VALIDITY);
        } else {
            validityDesc.append(endTime.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")));
        }
        return validityDesc.toString();
    }

    /**
     * 组装订单-评价信息视图
     * @param orderAppDetailPage
     * @param orderInfoDO
     */
    private void buildFeedbackInfoVO(OrderAppDetailPage orderAppDetailPage, OrderInfoDO orderInfoDO) {

        assembleFeedbackInfoVO(orderInfoDO, orderAppDetailPage);
    }

    /**
     * 组装订单信息视图
     *
     * @param orderAppDetailPage 视图对象
     * @param orderInfoDO        订单信息
     * @param orderItemCode      订单item编号
     */
    private void buildOrderInfoVO(OrderAppDetailPage orderAppDetailPage, OrderInfoDO orderInfoDO, String orderItemCode) {
        OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();

        orderInfo.setOrderCode(orderInfoDO.getOrderCode());
        // 订单状态供前端去筛选发票状态
        orderInfo.setOrderStatus(orderInfoDO.getOrderStatus());

        //TODO 优化
        OrderStatusMappingDO orderStatusMapping = getOrderStatusMapping(orderInfoDO, orderItemCode);
        if (orderStatusMapping != null) {
            orderInfo.setCustomerOrderStatusView(orderStatusMapping.getCustomerOrderStatusView());
            orderInfo.setCustomerAfterSalesOrderStatusView(orderStatusMapping.getCustomerAfterSalesOrderStatusView());
        }
        orderInfo.setOrderTime(orderInfoDO.getOrderTime());
        OrderPaymentRecordsDO orderPaymentRecordsDO = orderPaymentRecordsMapper.queryByLastOrderCode(orderInfoDO.getOrderCode(), orderInfoDO.getParentOrderCode());

        orderInfo.setPayTime(orderPaymentRecordsDO == null? null : orderPaymentRecordsDO.getPayFinishTime());
        orderInfo.setPaymentStatus(orderInfoDO.getPaymentStatus());
        orderInfo.setPaymentStatusDesc(PaymentStatusEnum.getDescriptionByCode(orderInfoDO.getPaymentStatus()));
        // 客户留言信息埋点
        orderInfo.setCustomerRemark(orderInfoDO.getCustomerRemark());

        // 在 orderInfoDO 中获取订单状态和订单时间
        Integer paymentStatus = orderInfoDO.getPaymentStatus();
        LocalDateTime orderTime = orderInfoDO.getOrderTime();

        // 查询发票状态
        CommonResult<List<InvoiceStatusVO>> result = null;
        try {
            result = invoiceApi.getInvokeStatusList(List.of(orderInfoDO.getOrderCode()));
        } catch (Exception e) {
            log.error("查询发票状态失败", e);
        }
        if (result != null && result.getData() != null) {
            List<InvoiceStatusVO> invoiceStatusVOS = result.getData();
            if (CollUtil.isNotEmpty(invoiceStatusVOS)) {
                orderInfo.setInvoiceStatus(invoiceStatusVOS.get(0).getInvoiceStatus());
            }
        }
        // 检查支付状态是否为"待支付"，并且当前时间在下单时间的150分钟之内
        if (PaymentStatusEnum.TO_BE_PAID.getCode().equals(paymentStatus)) {
            LocalDateTime paymentDeadline = orderTime.plusMinutes(VCS_PAY_TIMEOUT_MILLS/60/1000);
            LocalDateTime currentTime = LocalDateTime.now();

            // 如果当前时间在支付截止时间之前，计算剩余时间
            if (currentTime.isBefore(paymentDeadline)) {
                // 计算剩余时间并转换为时间戳
                long remainingTime = Duration.between(currentTime, paymentDeadline).toMillis();
                orderInfo.setPaymentTimeout(remainingTime);
            } else {
                // 如果当前时间已超过支付截止时间，设置剩余时间为 0 或 null
                // 或设置为 null，取决于需求
                orderInfo.setPaymentTimeout(0L);
            }
        }

        // 查看当前订单是否在 支付中
        String redisKey = Constants.PAYING_ORDER_KEY + orderInfoDO.getOrderCode();
        Object value = redisTemplate.opsForValue().get(redisKey);
        log.info("当前订单是否在支付中 redisKey:{},value:{}", redisKey, value);
        if (Objects.nonNull(value)) {
            orderInfo.setIsPaying(true);
        }

        setPayDownStatus(orderInfoDO, orderInfo);


        orderInfo.setPaymentMethod("微信支付");
        orderInfo.setCostAmount(MoneyUtil.convertFromCents(new BigDecimal(orderInfoDO.getCostAmount())));
        orderInfo.setOrderChannel(orderInfoDO.getOrderChannel());

        orderAppDetailPage.setOrderInfo(orderInfo);
    }

    private void setPayDownStatus(OrderInfoDO orderInfoDO, OrderBrandOrderInfoVO orderInfo) {
        orderInfo.setIsPayDown(true);
        //查看当前订单的回调中状态
        //1.Constants.SUCCESS_PAYING_ORDER_KEY + orderInfoDO.getOrderCode(); 查询是否存在
        String success_paying_order = Constants.SUCCESS_PAYING_ORDER_KEY + orderInfoDO.getOrderCode();
        String success_paying_parent = Constants.SUCCESS_PAYING_ORDER_KEY + orderInfoDO.getParentOrderCode();
        try {
            Boolean orderBoolean = redisTemplate.hasKey(success_paying_order);
            Boolean parentBoolean = redisTemplate.hasKey(success_paying_parent);
            //orderBoolean 和 parentBoolean 其中一个为true就取对应的value值判断是否为PENDING状态 ，是的话就设置为false
            if (Boolean.TRUE.equals(orderBoolean)) {
                String orderValue = (String) redisTemplate.opsForValue().get(success_paying_order);
                log.info("当前订单支付回调状态 redisKey:{},value:{}", success_paying_order, orderValue);
                if (OrderPaymentCallBackEnum.PENDING.getCode().equals(orderValue)) {
                    orderInfo.setIsPayDown(false);
                }
            }
            if (Boolean.TRUE.equals(parentBoolean)) {
                String parentValue = (String) redisTemplate.opsForValue().get(success_paying_parent);
                log.info("当前订单支付回调状态 redisKey:{},value:{}", success_paying_parent, parentValue);
                if (OrderPaymentCallBackEnum.PENDING.getCode().equals(parentValue)) {
                    orderInfo.setIsPayDown(false);
                }
            }
        }catch (Exception e){
            log.info("设置redisKey 出错,msg:{}",e.getMessage());
        }

    }

    private void buildNotVcsOrderInfoVO(OrderAppDetailPage orderAppDetailPage, OrderInfoDO orderInfoDO) {
        OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();
        orderInfo.setBusinessCode(orderInfoDO.getBusinessCode());
        orderInfo.setOrderCode(orderInfoDO.getOrderCode());
        // 订单状态供前端去筛选发票状态
        orderInfo.setOrderStatus(orderInfoDO.getOrderStatus());

        orderInfo.setOrderTime(orderInfoDO.getOrderTime());
        orderInfo.setPayTime(orderInfoDO.getPaymentTime());
        orderInfo.setPaymentStatus(orderInfoDO.getPaymentStatus());
        orderInfo.setPaymentStatusDesc(PaymentStatusEnum.getDescriptionByCode(orderInfoDO.getPaymentStatus()));
        orderInfo.setCompletedTime(orderInfoDO.getCompletedTime());
        orderInfo.setClosedTime(orderInfoDO.getClosedTime());
        //检查支付状态是否为"待支付"，并且当前时间在下单时间的15分钟之内
        LocalDateTime orderTime = orderInfoDO.getOrderTime();
        LocalDateTime paymentDeadline = orderTime.plusMinutes(BG_LRE_PAY_TIMEOUT_MILLS/60/1000);
        LocalDateTime currentTime = LocalDateTime.now();

        // 如果当前时间在支付截止时间之前，计算剩余时间
        if (currentTime.isBefore(paymentDeadline)) {
            // 计算剩余时间并转换为时间戳
            long remainingTime = Duration.between(currentTime, paymentDeadline).toMillis();
            orderInfo.setPaymentTimeout(remainingTime);
        } else {
            // 如果当前时间已超过支付截止时间，设置剩余时间为 0 或 null
            // 或设置为 null，取决于需求
            orderInfo.setPaymentTimeout(0L);
        }

        // 查询发票状态
        CommonResult<List<InvoiceStatusVO>> result = null;
        try {
            result = invoiceApi.getInvokeStatusListOnGeneral(List.of(orderInfoDO.getOrderCode()));
        } catch (Exception e) {
            log.error("查询发票状态失败", e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.INVOICE_GET_EXCEPTION);
        }
        if (result != null && result.getData() != null) {
            List<InvoiceStatusVO> invoiceStatusVOS = result.getData();
            if (CollUtil.isNotEmpty(invoiceStatusVOS)) {
                orderInfo.setInvoiceStatus(invoiceStatusVOS.get(0).getInvoiceStatus());
                AppInvoiceStatrusEnum statusDec = AppInvoiceStatrusEnum.getByCode(invoiceStatusVOS.get(0).getInvoiceStatus());
                orderInfo.setInvoiceStatusDesc(statusDec == null ? null : statusDec.getName());
            }
        }

        // 查看当前订单是否在 支付中
        String redisKey = Constants.PAYING_ORDER_KEY + orderInfoDO.getOrderCode();
        Object value = redisTemplate.opsForValue().get(redisKey);
        log.info("查询缓存确认是否在支付中 redisKey:{},value:{}", redisKey, value);
        if (Objects.nonNull(value)) {
            orderInfo.setIsPaying(true);
        }

        if(PaymentStatusEnum.PAID.getCode().equals(orderInfoDO.getPaymentStatus())){
            orderInfo.setPaymentMethod("微信支付");
        }
        orderInfo.setCostAmount(MoneyUtil.convertFromCents(new BigDecimal(orderInfoDO.getCostAmount())));
        orderInfo.setCostPoints(orderInfoDO.getPointAmount());
        orderInfo.setOrderChannel(orderInfoDO.getOrderChannel());
        orderInfo.setShipping(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getFreightAmount())));

        //映射小程序状态
        Map<Integer, String> statusMap = buildNotVcsOrderStatus(OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType()), orderInfoDO.getCouponStatus(), orderInfoDO.getLogisticsStatus());
        //statusMap的key作为状态码，value作为状态描述

        orderInfo.setOrderStatus(statusMap.keySet().iterator().next());
        orderInfo.setCustomerOrderStatusView(statusMap.get(orderInfo.getOrderStatus()));

        setCostAmountWithShipping(orderInfoDO, orderInfo);
        orderAppDetailPage.setOrderInfo(orderInfo);
    }
    /**
     * 组装车型信息
     */
    private void buildAdminCarInfoVO(ProductBrandCategoriedItemInfoVO itemInfoVO, OrderDetailRespVO orderDetailRespVO, List<String> orderItemCodes) {
        List<VCSOrderInfoDO> vcsOrderInfoDOList = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapper<VCSOrderInfoDO>()
                .in(VCSOrderInfoDO::getOrderItemCode, orderItemCodes)
                .eq(VCSOrderInfoDO::getIsDeleted, 0));
        if (CollUtil.isEmpty(vcsOrderInfoDOList)) {
            return;
        }
        // 取第一个车辆信息（假设每个carVin对应唯一的车辆信息）
        VCSOrderInfoDO vcsOrderInfo = vcsOrderInfoDOList.get(0);
        // 组装车辆信息
        OrderDetailVehicleInfoVO vehicleInfoVO = assembleVehicleInfoVO(vcsOrderInfo);
        orderDetailRespVO.setCarInfo(vehicleInfoVO);

        // key为orderItemCode value为VCSOrderInfoDO对象
        Map<String, VCSOrderInfoDO> orderItemCodeMap = vcsOrderInfoDOList.stream().collect(Collectors.toMap(VCSOrderInfoDO::getOrderItemCode, Function.identity()));

        // 设置服务启停时间和服务状态
        // 为普通商品时
        if (OrderItemSpuTypeEnum.NORMAL_GOOD.getCode().equals(itemInfoVO.getOrderItemSpuType())) {
            setServiceTimeAndStatusForItem(vcsOrderInfo.getOrderItemCode(), itemInfoVO, vcsOrderInfo);
            showCompleteOrderButton(itemInfoVO, orderDetailRespVO);
        } else {
            // 给组合商品设置服务启停时间和服务状态，捆绑商品不设置
            for (ProductBrandCategoriedItemInfoVO infoVO : itemInfoVO.getNext()) {
                String orderItemCode = infoVO.getOrderItemCode();
                VCSOrderInfoDO vcsInfo = orderItemCodeMap.get(orderItemCode);
                if (vcsInfo == null) {
                    continue;
                }
                setServiceTimeAndStatusForItem(orderItemCode, infoVO, vcsInfo);
                showCompleteOrderButton(infoVO, orderDetailRespVO);
            }
        }
    }

    /**
     * 根据商品品牌分类项信息和订单详情，决定是否显示完成订单按钮
     *
     * @param itemInfoVO 商品品牌分类项信息对象，包含服务状态描述等信息
     * @param orderDetailRespVO 订单详情对象，包含订单状态等信息
     */
    private static void showCompleteOrderButton(ProductBrandCategoriedItemInfoVO itemInfoVO, OrderDetailRespVO orderDetailRespVO) {
        // 如果服务状态为"激活失败"，且订单状态为已支付，则可以展示完成订单按钮
        if (OrderStatusEnum.PAID.getCode().equals(orderDetailRespVO.getOrderInfo().getOrderStatus())
                && FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getDesc().equals(itemInfoVO.getServiceStatusDesc())) {
            orderDetailRespVO.setShowCompleteOrderButton(true);
        }
    }

    /**
     * 组装车型信息
     */
    private void buildCarInfoVO(ProductBrandCategoriedItemInfoVO itemInfoVO, OrderAppDetailPage orderAppDetailPage, List<String> orderItemCodes) {
        //2. 组装车型信息
        OrderBrandVehicleInfoVO orderBrandVehicleInfoVO = new OrderBrandVehicleInfoVO();

        List<VCSOrderInfoDO> vcsOrderInfoDOList = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapper<VCSOrderInfoDO>()
                .in(VCSOrderInfoDO::getOrderItemCode, orderItemCodes)
                .eq(VCSOrderInfoDO::getIsDeleted, 0));
        if (CollUtil.isEmpty(vcsOrderInfoDOList)) {
            return;
        }
        VCSOrderInfoDO vcsOrderInfoDO = vcsOrderInfoDOList.get(0);
        // C端需要看到明文vin号
        String carVin = phoneNumberDecodeUtil.getDecodePhone(vcsOrderInfoDO.getCarVin());


        // 改造逻辑 保持订单详情获取车型编码、名字 和  订单列表一致
        orderBrandVehicleInfoVO.setCarVin(carVin);
        orderBrandVehicleInfoVO.setSeriesCode(vcsOrderInfoDO.getSeriesCode());
        orderBrandVehicleInfoVO.setSeriesName(vcsOrderInfoDO.getSeriesName());
        //判断缓存里面获取到的SeriesName是否为空，不为空取值seriesMapping里面的,否则原值
        Map<String, String> map = redisService.getCacheMap(REDIS_KEY.SERIES_CACHE_KEY);
        orderBrandVehicleInfoVO.setSeriesName(handleSeriesName(map,vcsOrderInfoDO.getSeriesCode(),vcsOrderInfoDO.getSeriesName()));
        orderAppDetailPage.setVehicleInfo(orderBrandVehicleInfoVO);

        // key为orderItemCode value为VCSOrderInfoDO对象
        Map<String, VCSOrderInfoDO> orderItemCodeMap = vcsOrderInfoDOList.stream().collect(Collectors.toMap(VCSOrderInfoDO::getOrderItemCode, Function.identity()));

        // 设置服务启停时间和服务状态
        // 为普通商品时
        if (OrderItemSpuTypeEnum.NORMAL_GOOD.getCode().equals(itemInfoVO.getOrderItemSpuType())) {
            //TODO t_vcs_order_info 记录的是单个商品 最新的服务启停时间，而不是单个商品服务启停时间的 历史记录
            setServiceTimeAndStatusForItem(vcsOrderInfoDO.getOrderItemCode(), itemInfoVO, vcsOrderInfoDO);
        } else {
            // 给组合商品设置服务启停时间和服务状态，捆绑商品不设置
            List<ProductBrandCategoriedItemInfoVO> childList = itemInfoVO.getNext();
            for (ProductBrandCategoriedItemInfoVO infoVO : childList) {
                String orderItemCode = infoVO.getOrderItemCode();
                VCSOrderInfoDO vcsInfo = orderItemCodeMap.get(orderItemCode);
                if (vcsInfo == null) {
                    continue;
                }
                setServiceTimeAndStatusForItem(orderItemCode, infoVO, vcsInfo);
            }
        }
        orderAppDetailPage.setProductItemInfo(itemInfoVO);
    }


    /**
     * 从缓存处理seriesName 有就取对应缓存 无就取原值
     * @param seriesCode 车型编码
     * @param seriesName 车型名称
     * @return 处理后的车型名称
     */
    private String handleSeriesName(Map<String, String> map ,String seriesCode, String seriesName) {
        String seriesMappingVOString = map.get(seriesCode);
        if(StringUtils.isNotBlank(seriesMappingVOString)){
            SeriesMappingVO seriesMappingVO = JSON.parseObject(seriesMappingVOString, SeriesMappingVO.class);
            return seriesMappingVO.getSeriesName()==null
                    ? seriesName
                    :seriesMappingVO.getSeriesName();

        }
        return seriesName;
    }

    @Override
    public List<OrderAppDetailPolicyRespVO> getAppOrderDetailPolicyList(String orderCode) {
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapper<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, 0));
        List<String> orderCodeList = new ArrayList<>();
        // 父单
        if (Objects.equals(orderInfoDO.getOrderType(), OrderTypeEnum.PARENT.getCode())) {
            List<OrderInfoDO> orderInfoDOList = orderInfoDOMapper.selectList(new LambdaQueryWrapper<OrderInfoDO>()
                    .eq(OrderInfoDO::getParentOrderCode, orderCode)
                    .eq(OrderInfoDO::getIsDeleted, 0));
            orderCodeList = orderInfoDOList.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
        } else {
            orderCodeList.add(orderCode);
        }
        // 1.通过orderItemCode去t_order_terms表中查询所有的条款信息，进行去重,组装成 List<terms_code>
        List<OrderTermsDO> orderTermsDOS = orderTermsDOMapper.selectList(new LambdaQueryWrapper<OrderTermsDO>()
                .in(OrderTermsDO::getOrderCode, orderCodeList)
                .eq(OrderTermsDO::getIsDeleted, 0));

        if (CollUtil.isEmpty(orderTermsDOS)) {
            return null;
        }

        //对termsCode去重 去空处理
        List<String> policyCodeList = new ArrayList<>();
        orderTermsDOS.stream()
                .map(OrderTermsDO::getTermsCode)
                .distinct() // 使用 Stream API 进行去重
                .forEach(termsCode -> {
                    if (StringUtils.isNotBlank(termsCode)) { // 使用 if 语句进行额外的空白检查
                        policyCodeList.add(termsCode);
                    }
                });

        // 2.调用feign接口
        if (CollUtil.isEmpty(policyCodeList)) {
            return null;
        }

        CommonResult<List<OrderDetailPolicyRespVO>> policyList = null;
        try {
            policyList = policyApi.getPolicyList(policyCodeList);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (policyList == null || policyList.getData() == null || CollUtil.isEmpty(policyList.getData())) {
            return null;
        }

        // 3.组装响应数据
        return policyList.getData().stream().map(
                policy -> {
                    OrderAppDetailPolicyRespVO orderAppDetailPolicyRespVO = new OrderAppDetailPolicyRespVO();
                    if (policy != null) {
                        orderAppDetailPolicyRespVO.setPolicyCode(policy.getPolicyCode());
                        orderAppDetailPolicyRespVO.setPolicyName(policy.getPolicyName());
                        orderAppDetailPolicyRespVO.setPolicyContent(policy.getPolicyContent());
                    }
                    return orderAppDetailPolicyRespVO;
                }
        ).collect(Collectors.toList());
    }

    @Override
    public List<OrderNewBrandRespVO> getNewOrderList(OrderBrandListNewDTO dto, Integer orderChannel) {
        String consumerCode = dto.getConsumerCode();
        List<String> seriesCodeList = dto.getSeriesCodeList();

        List<OrderNewBrandRespVO> result = new ArrayList<>();

        //todo 这里的组合商品是是什么意思，似乎和BG LRE不是一个纬度

        // 0.根据consumerCode和seriesCodeList查询t_vcs_order_info
        List<VCSOrderInfoDO> vcsOrderInfoDOList = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapper<VCSOrderInfoDO>()
                .eq(VCSOrderInfoDO::getConsumerCode, consumerCode)
                .in(VCSOrderInfoDO::getSeriesCode, seriesCodeList)
                .eq(VCSOrderInfoDO::getIsDeleted, 0));
        if (CollUtil.isEmpty(vcsOrderInfoDOList)) {
            log.info("查询订单列表时, vcsOrderInfoDOList为空, consumerCode={}", consumerCode);
            return result;
        }
        // 一个订单对应一个vcs信息
        Map<String, VCSOrderInfoDO> vcsOrderInfoDOMap = vcsOrderInfoDOList.stream().collect(Collectors.toMap(VCSOrderInfoDO::getOrderCode, Function.identity(), (o, n) -> o));

        // a.根据orderCode去t_order_info查询非父订单数据
        Integer mLROrderChannelCode= LR_WECHAT.getOrderChannelCode();
        Integer mJAOrderChannelCode= JA_WECHAT.getOrderChannelCode();
        Integer pcCustomerServiceOrderChannelCode= CUSTOMER_SERVICE.getOrderChannelCode();
        Set<String> orderCodeSet = vcsOrderInfoDOMap.keySet();
        List<OrderInfoDO> orderInfoDOS = orderInfoDOMapper.selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                // 使用 in 操作符替代 eqIfPresent，以支持多个渠道
//                .in(OrderInfoDO::getOrderChannel,
//                        Objects.equals(orderChannel, mLROrderChannelCode) ? Arrays.asList(mLROrderChannelCode, pcCustomerServiceOrderChannelCode) : // 路虎小程序及代客下单
//                        Objects.equals(orderChannel, mJAOrderChannelCode) ? Arrays.asList(mJAOrderChannelCode, pcCustomerServiceOrderChannelCode) : // 捷豹小程序及代客下单
//                        Collections.singletonList(orderChannel)) // 其他情况仅查询指定渠道
                .in(OrderInfoDO::getOrderCode, orderCodeSet)
                .ne(OrderInfoDO::getOrderType, OrderTypeEnum.PARENT.getCode())
                .eq(OrderInfoDO::getIsDeleted, 0));
        /*        Map<String, OrderInfoDO> orderInfoDOMap = orderInfoDOS.stream()
                .collect(Collectors.toMap(OrderInfoDO::getOrderCode, Function.identity()));*/
        if (CollUtil.isEmpty(orderInfoDOS)) {
            log.info("查询订单列表时, orderInfoDOS为空, orderCodeSet={}, orderChannel={}", orderCodeSet, orderChannel);
        }
        // 过滤出订单中为组合商品的订单
        Set<String> bundleOrderCodeSet = orderInfoDOS.stream().filter(orderInfoDO -> OrderTypeEnum.BUNDLED_GOODS.getCode().equals(orderInfoDO.getOrderType()))
                .map(OrderInfoDO::getOrderCode).collect(Collectors.toSet());

/*        // b.根据orderItemCode去t_order_item查询所有商品数据
        List<String> orderItemCodeList = vcsOrderInfoDOList.stream().map(VCSOrderInfoDO::getOrderItemCode).collect(Collectors.toList());
        List<OrderItemDO> orderItemDOS = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                .in(OrderItemDO::getOrderItemCode, orderItemCodeList)
                .eq(OrderItemDO::getIsDeleted, 0));
        Map<String, OrderItemDO> orderItemDOSMap = orderItemDOS.stream()
                .collect(Collectors.toMap(OrderItemDO::getOrderItemCode, Function.identity()));*/

        // b.根据orderCode去t_order_item查询所有商品数据
        List<OrderItemDO> orderItemDOS = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                .in(OrderItemDO::getOrderCode, orderCodeSet)
                .eq(OrderItemDO::getIsDeleted, 0));

        // 存在组合商品，需要做过滤
        orderItemDOS = getOrderItemDOS(bundleOrderCodeSet, orderItemDOS);
        Map<String, OrderItemDO> orderItemDOSMap = orderItemDOS.stream()
                .collect(Collectors.toMap(OrderItemDO::getOrderCode, Function.identity(), (o, n) -> o));

        // c.得到所有orderItemcode对应的退款状态
        List<String> orderItemCodeList = orderItemDOS.stream().map(OrderItemDO::getOrderItemCode).collect(Collectors.toList());
        List<OrderRefundStatusMapDTO> refundOrderStatusByOrderItemCodeList = orderRefundItemDOMapper.getRefundOrderStatusByOrderItemCodeList(orderItemCodeList);
        Map<String, Integer> refundOrderStatusMap = refundOrderStatusByOrderItemCodeList.stream().collect(Collectors.toMap(OrderRefundStatusMapDTO::getOrderItemCode, OrderRefundStatusMapDTO::getRefundOrderStatus));

        // d.查t_order_status_mapping的全量
        List<OrderStatusMappingDO> orderStatusMappingDOList = orderStatusMappingDOMapper.selectList(new LambdaQueryWrapper<OrderStatusMappingDO>()
                .eq(OrderStatusMappingDO::getIsDeleted, 0));
        //根据order_status和refund_order_status 作为一个组合key进行分组
        Map<String, OrderStatusMappingDO> orderStatusMappingMap = orderStatusMappingDOList.stream()
                .collect(Collectors.toMap(item -> item.getOrderStatus() + "_" + item.getRefundOrderStatus(), Function.identity()));

        // e.批量查询订单开票状态
        Map<String, InvoiceStatusVO> invoiceStatusVOMap = getInvoiceStatusMap(new ArrayList<>(orderCodeSet));

        // sprint47 f.根据orderInfoDOS中 orderCode对应的orderStatus 去查t_feedback_records是否有 这个订单当前环节的评价记录
        List<FeedbackRecordsDO> feedbackRecordsDOList = feedbackRecordsDOMapper.selectList(new LambdaQueryWrapper<FeedbackRecordsDO>()
                .in(FeedbackRecordsDO::getOrderCode, orderCodeSet)
                .eq(FeedbackRecordsDO::getIsDeleted, 0));
        // 构建 Map<String, List<FeedbackRecordsDO>>，key 是 orderCode，value 是该订单的所有评价记录
        Map<String, List<FeedbackRecordsDO>> feedbackRecordsMap = feedbackRecordsDOList.stream().collect(Collectors.groupingBy(FeedbackRecordsDO::getOrderCode));

        // g. 查是否有启用的 评价配置
        List<FeedbackConfigDO> feedbackConfigDOList = feedbackConfigDOMapper.selectList(new LambdaQueryWrapper<FeedbackConfigDO>()
                .eq(FeedbackConfigDO::getIsDeleted, 0)
                .eq(FeedbackConfigDO::getEnableStatus, FeedBackEnableStatusEnum.ENABLE.getCode()));

        List<String> carVinList = vcsOrderInfoDOList.stream().map(VCSOrderInfoDO::getCarVin).distinct().collect(Collectors.toList());

        Map<String, String> decodeListText = piplDataUtil.getDecodeListText(carVinList);
        Map<String, String> map = redisService.getCacheMap(REDIS_KEY.SERIES_CACHE_KEY);
        orderInfoDOS.forEach(orderInfoDO -> {
            OrderNewBrandRespVO orderNewBrandRespVO = new OrderNewBrandRespVO();
            String orderCode = orderInfoDO.getOrderCode();
            //1.组装商品信息
            VCSOrderInfoDO vcsOrderInfoDO = vcsOrderInfoDOMap.get(orderCode);
            OrderItemDO orderItemDO = orderItemDOSMap.get(orderCode);
            if (jungleVCSandOrderItem(orderCode, vcsOrderInfoDO, orderItemDO)) {
                return;
            }
            String orderItemCode = orderItemDO.getOrderItemCode();
            orderNewBrandRespVO.setProductInfo(assembleProductInfo(orderItemDO));

            //2.组装车型信息
            OrderBrandVehicleInfoVO vehicleInfo = new OrderBrandVehicleInfoVO();
            // C端需要看到明文vin号

            String carVin = decodeListText.get(vcsOrderInfoDO.getCarVin());
            vehicleInfo.setCarVin(carVin);
            vehicleInfo.setSeriesCode(vcsOrderInfoDO.getSeriesCode());
            vehicleInfo.setSeriesName(handleSeriesName(map,vcsOrderInfoDO.getSeriesCode(),vcsOrderInfoDO.getSeriesName()));
            orderNewBrandRespVO.setVehicleInfo(vehicleInfo);

            //3.组装订单信息
            OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();

            //映射小程序状态
            Integer orderStatus = getOrderStatus(refundOrderStatusMap, orderStatusMappingMap, orderInfoDO, orderItemCode, orderInfo);

            orderInfo.setOrderCode(orderCode);
            orderInfo.setOrderType(orderInfoDO.getOrderType());

            // 供前端去筛选是否显示 查看发票 开发票功能
            orderInfo.setOrderStatus(orderStatus);
            assembleInvoiceStatusAndCost(invoiceStatusVOMap, orderInfo, orderItemDO);

            orderInfo.setOrderTime(orderInfoDO.getOrderTime());
            // 组合商品订单不展示服务时间
            if (!OrderTypeEnum.BUNDLED_GOODS.getCode().equals(orderInfoDO.getOrderType())) {
                orderInfo.setServiceBeginDate(vcsOrderInfoDO.getServiceBeginDate());
                orderInfo.setServiceEndDate(vcsOrderInfoDO.getServiceEndDate());
            }
            orderInfo.setOrderChannel(orderInfoDO.getOrderChannel());
            orderNewBrandRespVO.setOrderInfo(orderInfo);
            orderNewBrandRespVO.setOrderCode(orderCode);
            orderNewBrandRespVO.setOrderTime(orderInfoDO.getOrderTime());
            setPayDownStatus(orderInfoDO, orderInfo);
            //4.组装订单标签信息
            orderNewBrandRespVO.setTagInfo(assembleTagInfo(orderInfoDO, orderItemCode, refundOrderStatusMap, feedbackRecordsMap.get(orderCode),feedbackConfigDOList));

            result.add(orderNewBrandRespVO);
        });


        //按下单时间排序
        if (!CollectionUtils.isEmpty(result)) {
            try {
                result.sort(Comparator.comparing(OrderNewBrandRespVO::getOrderTime).reversed());
//                log.info("排序结果:{}", result);
            } catch (Exception e) {
                log.info("排序报错 msg:{} ,如果null就是orderTime有脏数据！", e.getMessage());
            }
        }

        return result;
    }

    /**
     * 组装订单标签信息
     *
     * @param orderInfoDO
     * @param refundOrderStatusMap
     * @param feedbackRecordsDOS
     * @param feedbackConfigDOList
     * @return
     */
    private OrderTagInfoVO assembleTagInfo(OrderInfoDO orderInfoDO, String orderItemCode, Map<String, Integer> refundOrderStatusMap, List<FeedbackRecordsDO> feedbackRecordsDOS, List<FeedbackConfigDO> feedbackConfigDOList) {
        OrderTagInfoVO tagInfo = new OrderTagInfoVO();

        // 判断是否属于退款/售后
        boolean isRefundOrAfterSale = checkIsRefundOrAfterSale(orderItemCode, refundOrderStatusMap);
        tagInfo.setIsRefundOrAfterSale(isRefundOrAfterSale);

        // 判断是否待评价
        boolean isPendingEvaluation = checkIsPendingEvaluation(orderInfoDO, feedbackRecordsDOS, feedbackConfigDOList);
        tagInfo.setIsPendingEvaluation(isPendingEvaluation);

        return tagInfo;
    }

    /**
     * 组装订单标签信息
     *
     * @param orderInfoDO
     * @param orderItemCode
     * @param feedbackRecordsDOS
     * @param feedbackConfigDOList
     * @return
     */
    private OrderTagInfoVO assembleTagInfoForVcs(OrderInfoDO orderInfoDO, String orderItemCode, boolean isRefundOrAfterSale, List<FeedbackRecordsDO> feedbackRecordsDOS, List<FeedbackConfigDO> feedbackConfigDOList) {
        OrderTagInfoVO tagInfo = new OrderTagInfoVO();

        // 判断是否属于退款/售后
        tagInfo.setIsRefundOrAfterSale(isRefundOrAfterSale);

        // 判断是否待评价
        boolean isPendingEvaluation = checkIsPendingEvaluation(orderInfoDO, feedbackRecordsDOS, feedbackConfigDOList);
        tagInfo.setIsPendingEvaluation(isPendingEvaluation);

        return tagInfo;
    }

    /**
     * 判断是否属于退款/售后
     *
     * @param orderItemCode
     * @param refundOrderStatusMap
     * @return
     */
    private boolean checkIsRefundOrAfterSale(String orderItemCode, Map<String, Integer> refundOrderStatusMap) {
        // 查询退款状态表 根据orderItemCode查询退款状态
        Integer refundStatus = refundOrderStatusMap.get(orderItemCode);
        return refundStatus != null;
    }

    /**
     * 判断是否待评价
     *
     * @param orderInfoDO
     * @param feedbackRecords
     * @param feedbackConfigDOList
     * @return
     */
    private boolean checkIsPendingEvaluation(OrderInfoDO orderInfoDO, List<FeedbackRecordsDO> feedbackRecords, List<FeedbackConfigDO> feedbackConfigDOList) {
        log.info("checkIsPendingEvaluation orderInfoDO:{}, feedbackRecords:{}, feedbackConfigDOList:{}", orderInfoDO, feedbackRecords, feedbackConfigDOList);

        // 1. 检查订单状态是否满足待评价条件（eg.已支付、订单完成、订单整单取消等）
        Integer orderStatus = orderInfoDO.getOrderStatus();
        List<Integer> evaluableStatuses = Arrays.asList( OrderStatusEnum.COMPLETED.getCode(), OrderStatusEnum.FULLY_CANCELLED.getCode());
        if (!evaluableStatuses.contains(orderStatus)) { // 订单状态不在可评价范围内，直接返回false
            return false;
        }

        // 2. 查询是否有已启用的评价设置
        String feedbackDimensions = FeedBackTypeEnum.getFeedbackDimensionsByOrderStatus(orderStatus);
        if (feedbackDimensions == null) {
            return false; // 如果没有找到对应的评价维度，直接返回false
        }
        boolean hasEnabledFeedbackConfig = false;
        for (FeedbackConfigDO config : feedbackConfigDOList) {
            if (config.getFeedbackDimensions().equals(feedbackDimensions) && Objects.equals(config.getEnableStatus(), FeedBackEnableStatusEnum.ENABLE.getCode())) {
                hasEnabledFeedbackConfig = true;
                break;
            }
        }
        if (!hasEnabledFeedbackConfig) {
            return false; // 如果没有找到符合条件的评价配置，直接返回false
        }

        // 3. 查询该订单是否有评价记录
        if (CollUtil.isEmpty(feedbackRecords)) {
            return true; // 如果没有评价记录，则认为是待评价
        }
        // 进一步检查是否有符合当前环节的评价记录
        for (FeedbackRecordsDO record : feedbackRecords) {
//            if (FeedBackTypeEnum.PAID.getCode().equals(record.getFeedbackDimensions()) && OrderStatusEnum.PAID.getCode().equals(orderStatus)) {
//                return false; // 订单状态为'已支付' 有'已支付'评价记录，不是待评价
//            }
            if (FeedBackTypeEnum.COMPLETED.getCode().equals(record.getFeedbackDimensions()) && OrderStatusEnum.COMPLETED.getCode().equals(orderStatus)) {
                return false; //  订单状态为'订单完成' 有'订单完成'评价记录，不是待评价
            }
            if (FeedBackTypeEnum.FULLY_CANCELLED.getCode().equals(record.getFeedbackDimensions()) && OrderStatusEnum.FULLY_CANCELLED.getCode().equals(orderStatus)) {
                return false; // 订单状态为'订单整单取消' 有'订单整单取消'评价记录，不是待评价
            }
        }

        return true; // 没有找到符合条件的评价记录，认为是待评价
    }

    private static boolean jungleVCSandOrderItem(String orderCode, VCSOrderInfoDO vcsOrderInfoDO, OrderItemDO orderItemDO) {
        if (vcsOrderInfoDO == null) {
            log.warn("orderCode = {} 对应的vcsOrderInfoDO为空", orderCode);
            return true;
        }

        if (orderItemDO == null) {
            log.warn("orderCode = {} 对应的orderItemDO为空", orderCode);
            return true;
        }
        return false;
    }

    private static Integer getOrderStatus(Map<String, Integer> refundOrderStatusMap, Map<String, OrderStatusMappingDO> orderStatusMappingMap, OrderInfoDO orderInfoDO, String orderItemCode, OrderBrandOrderInfoVO orderInfo) {
        Integer orderStatus = orderInfoDO.getOrderStatus();
        Integer refundOrderStatus = refundOrderStatusMap.get(orderItemCode);
        if (refundOrderStatus == null) {
            refundOrderStatus = -1;
        }
        OrderStatusMappingDO orderStatusMapping = orderStatusMappingMap.get(orderStatus + "_" + refundOrderStatus);
        if (orderStatusMapping != null) {
            orderInfo.setCustomerOrderStatusView(orderStatusMapping.getCustomerOrderStatusView());
        }
        return orderStatus;
    }

    private static Integer getOrderStatus(Integer refundOrderStatus, Map<String, OrderStatusMappingDO> orderStatusMappingMap, OrderBrandOrderInfoVO orderInfo) {
        Integer orderStatus = orderInfo.getOrderStatus();
        if (refundOrderStatus == null) {
            refundOrderStatus = -1;
        }
        OrderStatusMappingDO orderStatusMapping = orderStatusMappingMap.get(orderStatus + "_" + refundOrderStatus);
        if (orderStatusMapping != null) {
            orderInfo.setCustomerOrderStatusView(orderStatusMapping.getCustomerOrderStatusView());
        }
        return orderStatus;
    }

    /***
     * <AUTHOR>
     * @description 获取非VSC商品的订单状态展示
     * order type如果是0（即不是父单）,则展示为未支付。注意！！！只能基于selectConsumerOrderPage查出来的数据适用此规则
     * 如果order_type不是0（说明不是父单）:则去对应的xxx_status字段的值展示即可
     * @date 2025/3/8 20:46
     * @param orderType: 订单类型
     * @param extendStatus: 扩展订单状态
     * @return: java.lang.Integer
    */
    //TODO 具体的枚举值定义待定
    private static Integer getNotVcsOrderStatus(Integer orderType, Integer extendStatus) {
        if (orderType == 0) {
            return 0;//未支付
        }else{
            return extendStatus;
        }
    }

    private List<OrderItemDO> getOrderItemDOS(Set<String> bundleOrderCodeSet, List<OrderItemDO> orderItemDOS) {
        if (CollUtil.isNotEmpty(bundleOrderCodeSet)) {
            orderItemDOS = orderItemDOS.stream().filter(orderItemDO -> {
                // 如果订单是组合商品，只保留组合商品本身
                if (bundleOrderCodeSet.contains(orderItemDO.getOrderCode())) {
                    return OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode().equals(orderItemDO.getOrderItemSpuType());
                }
                return true;
            }).collect(Collectors.toList());
        }
        return orderItemDOS;
    }

    private List<OrderItemRefundVcsPo> getOrderItemDOSVcs(Set<String> bundleOrderCodeSet, List<OrderItemRefundVcsPo> orderItemDOS) {
        if (CollUtil.isNotEmpty(bundleOrderCodeSet)) {
            orderItemDOS = orderItemDOS.stream().filter(orderItemDO -> {
                // 如果订单是组合商品，只保留组合商品本身
                if (bundleOrderCodeSet.contains(orderItemDO.getOrderCode())) {
                    if (orderItemDO.getOrderItemSpuType() == null){
                        return false;
                    }
                    return OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode().equals(orderItemDO.getOrderItemSpuType());
                }
                return true;
            }).collect(Collectors.toList());
        }
        return orderItemDOS;
    }

    /**
     * 组装发票状态和实付金额
     *
     * @param invoiceStatusVOMap 发票状态集合
     * @param orderInfo          订单信息
     * @param orderItemDO        订单数据
     */
    private static void assembleInvoiceStatusAndCostUpdated(Map<String, InvoiceStatusVO> invoiceStatusVOMap, OrderBrandOrderInfoVO orderInfo, OrderItemRefundVcsPo orderItemDO) {
        String orderCode = orderInfo.getOrderCode();
        if (CollUtil.isNotEmpty(invoiceStatusVOMap) && invoiceStatusVOMap.containsKey(orderCode)) {
            InvoiceStatusVO invoiceStatusVO = invoiceStatusVOMap.get(orderCode);
            if (Objects.nonNull(invoiceStatusVO)) {
                orderInfo.setInvoiceStatus(invoiceStatusVO.getInvoiceStatus());
            }
        }
        if (Objects.nonNull(orderItemDO) && orderItemDO.getCostAmount() != null) {
            orderInfo.setCostAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderItemDO.getCostAmount())));
            orderInfo.setCostAmountIncludeShipping(orderInfo.getCostAmount());//VCS的商品没有运费，所以含运费总价就等于不含运费的总价
        }
    }

    /**
     * 组装发票状态和实付金额
     *
     * @param invoiceStatusVOMap 发票状态集合
     * @param orderInfo          订单信息
     * @param orderItemDO        订单数据
     */
    private static void assembleInvoiceStatusAndCost(Map<String, InvoiceStatusVO> invoiceStatusVOMap, OrderBrandOrderInfoVO orderInfo, OrderItemDO orderItemDO) {
        String orderCode = orderInfo.getOrderCode();
        if (CollUtil.isNotEmpty(invoiceStatusVOMap) && invoiceStatusVOMap.containsKey(orderCode)) {
            InvoiceStatusVO invoiceStatusVO = invoiceStatusVOMap.get(orderCode);
            if (Objects.nonNull(invoiceStatusVO)) {
                orderInfo.setInvoiceStatus(invoiceStatusVO.getInvoiceStatus());
            }
        }
        if (Objects.nonNull(orderItemDO) && orderItemDO.getCostAmount() != null) {
            orderInfo.setCostAmount(MoneyUtil.convertFromCents(new BigDecimal(orderItemDO.getCostAmount())));
        }
    }

    /**
     * 组装商品信息
     *
     * @param orderItemDO 订单item数据
     * @return 订单item基础信息
     */
    private OrderItemBaseVO assembleProductInfo(OrderItemDO orderItemDO) {
        OrderItemBaseVO productInfo = new OrderItemBaseVO();
        if (Objects.nonNull(orderItemDO)) {
            productInfo.setOrderItemCode(orderItemDO.getOrderItemCode());
            productInfo.setProductVersionCode(orderItemDO.getProductVersionCode());
            productInfo.setProductCode(orderItemDO.getProductCode());
            productInfo.setProductSkuCode(orderItemDO.getProductSkuCode());

            productInfo.setProductName(orderItemDO.getProductName());
            productInfo.setProductImageUrl(orderItemDO.getProductImageUrl());
            productInfo.setProductAttribute(AttributeUtil.formatProductAttributes(orderItemDO.getProductAttribute()));

            if (orderItemDO.getProductMarketPrice() != null) {
                productInfo.setProductMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(orderItemDO.getProductMarketPrice())));
            }

            productInfo.setProductSalePrice(MoneyUtil.convertFromCents(new BigDecimal(orderItemDO.getProductSalePrice())));
            productInfo.setProductQuantity(orderItemDO.getProductQuantity());
        }
        return productInfo;
    }

    /**
     * 获取发票状态集合
     *
     * @param orderInfoDOList 订单号列表
     * @return 发票状态集合
     */
    private Map<String, InvoiceStatusVO> getInvoiceStatusMap2(List<OrderInfoDO> orderInfoDOList) {
        if (CollUtil.isEmpty(orderInfoDOList)) {
            return new HashMap<>();
        }

        List<String> vcsOrderCodes = new ArrayList<>();
        List<String> notVcsOrderCodes = new ArrayList<>();
        for (OrderInfoDO orderInfo:orderInfoDOList) {
            if (BusinessIdEnum.VCS.getCode().equals(orderInfo.getBusinessCode())) {
                vcsOrderCodes.add(orderInfo.getOrderCode());
            } else {
                notVcsOrderCodes.add(orderInfo.getOrderCode());
            }
        }

        Map<String, InvoiceStatusVO> invoiceStatusVOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(vcsOrderCodes)) {
            Map<String, InvoiceStatusVO> vcsInvoiceStatusMap = getInvoiceStatusMap(vcsOrderCodes);
            if (MapUtil.isNotEmpty(vcsInvoiceStatusMap)) {
                invoiceStatusVOMap.putAll(vcsInvoiceStatusMap);
            }
        }

        if (CollUtil.isNotEmpty(notVcsOrderCodes)) {
            try {
                putInvoiceStatusIntoMap(notVcsOrderCodes, invoiceStatusVOMap);
            } catch (Exception e) {
                log.error("查询订单开票状态异常，notVcsOrderCodes={}", JsonUtils.toJsonString(notVcsOrderCodes), e);
            }
        }
        return invoiceStatusVOMap;
    }

    private void putInvoiceStatusIntoMap(List<String> notVcsOrderCodes, Map<String, InvoiceStatusVO> invoiceStatusVOMap) {
        CommonResult<List<InvoiceStatusVO>> commonResult = invoiceApi.getInvokeStatusListOnGeneral(notVcsOrderCodes);
        if (commonResult != null && commonResult.getData() != null) {
            List<InvoiceStatusVO> invoiceStatusVOS = commonResult.getData();
            if (CollUtil.isNotEmpty(invoiceStatusVOS)) {
                for (InvoiceStatusVO invoiceStatus:invoiceStatusVOS) {
                    invoiceStatusVOMap.put(invoiceStatus.getOrderCode(), invoiceStatus);
                }
            }
        }
    }

    /**
     * 获取发票状态集合
     *
     * @param orderCodeList 订单号列表
     * @return 发票状态集合
     */
    private Map<String, InvoiceStatusVO> getInvoiceStatusMap(List<String> orderCodeList) {
        Map<String, InvoiceStatusVO> invoiceStatusVOMap = null;
        CommonResult<List<InvoiceStatusVO>> commonResult = null;
        try {
            commonResult = invoiceApi.getInvokeStatusList(orderCodeList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (commonResult != null && commonResult.getData() != null) {
            List<InvoiceStatusVO> invoiceStatusVOS = commonResult.getData();
            if (CollUtil.isNotEmpty(invoiceStatusVOS)) {
                invoiceStatusVOMap = invoiceStatusVOS.stream()
                        .collect(Collectors.toMap(InvoiceStatusVO::getOrderCode, Function.identity()));
            }
        }
        return invoiceStatusVOMap;
    }

    @Override
    public OrderIntegrationRespVO getOrder(String orderCode, String jlrId) {
        OrderIntegrationRespVO orderIntegrationRespVO = new OrderIntegrationRespVO();

        CommonResult<ConsumerInfoDTO> consumerByCode = null;
        try {
            consumerByCode = consumerApi.getConsumerByCode(jlrId);
        } catch (Exception e) {
            log.error("调用consumerApi异常:{}",e);
        }
        if (consumerByCode != null && consumerByCode.getData() != null) {
            String phoneEncrypt = consumerByCode.getData().getPhoneEncrypt();
            String decodePhone = phoneNumberDecodeUtil.getDecodePhone(phoneEncrypt);
            orderIntegrationRespVO.setPhoneNumber(decodePhone);
        }

        //如果orderCode为空，直接返回
        if (StringUtils.isBlank(orderCode)) {
            return orderIntegrationRespVO;
        }

        // 1.查询订单信息
        OrderInfoDO orderInfoDO = orderInfoDOMapper.queryOrderDoByOrderCode(orderCode);
        if (Objects.isNull(orderInfoDO)) {
            return orderIntegrationRespVO;
        }
        orderIntegrationRespVO.setOrderCode(orderCode);
        orderIntegrationRespVO.setOrderTime(orderInfoDO.getOrderTime());
        //判断业务线，查询对应信息item信息
        getCrcOOrderInfo(orderIntegrationRespVO,orderInfoDO);
        log.info("CRC订单详情返回参数orderIntegrationRespVO={}", orderIntegrationRespVO);
        return orderIntegrationRespVO;
    }

    /**
     * 获取CRC订单信息
     *
     * @param orderIntegrationRespVO 订单信息
     * @param orderInfoDO 订单DO
     */
    /**
     * 根据订单信息组装 VCS 或 BG 类型商品的集成响应数据
     *
     * @param orderIntegrationRespVO 响应对象，用于装载 VCS 或 BG 的详细信息
     * @param orderInfoDO            当前订单信息
     */
    private void getCrcOOrderInfo(OrderIntegrationRespVO orderIntegrationRespVO, OrderInfoDO orderInfoDO) {
        // 获取订单编号
        String orderCode = orderInfoDO.getOrderCode();

        // 判断当前订单属于哪个业务线，并分别处理
        if (BusinessIdEnum.VCS.getCode().equals(orderInfoDO.getBusinessCode())) {
            handleVcsOrder(orderIntegrationRespVO, orderCode, orderInfoDO);
        } else {
            handleBrandGoodsOrder(orderIntegrationRespVO, orderCode, orderInfoDO);
        }
    }

    /**
     * 处理 VCS 类型订单的信息组装
     *
     * @param orderIntegrationRespVO 响应对象，用于装载 VCS 商品信息
     * @param orderCode              订单编号
     * @param orderInfoDO            当前订单信息
     */
    private void handleVcsOrder(OrderIntegrationRespVO orderIntegrationRespVO, String orderCode, OrderInfoDO orderInfoDO) {
        // 查询主商品项
        Integer spuType = OrderItemSpuTypeEnum.NORMAL_GOOD.getCode();
        if (OrderTypeEnum.BUNDLED_GOODS.getCode().equals(orderInfoDO.getOrderType())) {
            spuType = OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode();
        }

        OrderItemDO orderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getOrderItemSpuType, spuType)
                .eq(OrderItemDO::getIsDeleted, 0)
                .orderByDesc(OrderItemDO::getId)
                .last(LIMIT_ONE));

        if (orderItemDO == null) {
            return;
        }

        VcsInfo vcsInfo = new VcsInfo();
        vcsInfo.setProductImageUrl(orderItemDO.getProductImageUrl());
        vcsInfo.setProductName(orderItemDO.getProductName());
        vcsInfo.setProductAttribute(AttributeUtil.formatProductAttributes(orderItemDO.getProductAttribute()));
        vcsInfo.setProductSalePrice(MoneyUtil.convertFromCents(new BigDecimal(orderInfoDO.getCostAmount())));
        vcsInfo.setProductQuantity(String.valueOf(Optional.ofNullable(orderItemDO.getProductQuantity()).orElse(1)));

        // 查询 VCS 车辆信息
        VCSOrderInfoDO vcsOrderInfoDO = vcsOrderInfoDOMapper.selectOne(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                .eq(VCSOrderInfoDO::getOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(VCSOrderInfoDO::getId)
                .last(LIMIT_ONE));

        if (vcsOrderInfoDO != null) {
            String carVin = piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin());
            vcsInfo.setCarVin(carVin);

            // 查询车辆详情并填充到响应中
            fetchAndSetVehicleDetails(vcsInfo, carVin, vcsOrderInfoDO);
        }

        orderIntegrationRespVO.setVcsInfo(vcsInfo);
    }

    /**
     * 查询车辆信息并设置到 VcsInfo 对象中
     *
     * @param vcsInfo         VCS 响应对象
     * @param carVin          车辆 VIN 号
     * @param vcsOrderInfoDO  VCS 订单信息 DO
     */
    private void fetchAndSetVehicleDetails(VcsInfo vcsInfo, String carVin, VCSOrderInfoDO vcsOrderInfoDO) {
        CommonResult<IcrVehicleRespVO> commonResult = icrVehicleApi.view(carVin);
        if (commonResult != null && commonResult.isSuccess()) {
            IcrVehicleRespVO icrVehicleRespVO = commonResult.getData();
            if (icrVehicleRespVO != null) {
                vcsInfo.setModelYear(icrVehicleRespVO.getModelYear());
                vcsInfo.setBrandCode(icrVehicleRespVO.getBrandCode());
                vcsInfo.setBrandName(icrVehicleRespVO.getBrandName());
                vcsInfo.setConfigCode(icrVehicleRespVO.getConfigCode());
                vcsInfo.setConfigName(icrVehicleRespVO.getConfigName());
                vcsInfo.setSeriesCode(vcsOrderInfoDO.getSeriesCode());
                vcsInfo.setSeriesName(vcsOrderInfoDO.getSeriesName());
                vcsInfo.setHobEn(icrVehicleRespVO.getHobEn());
                vcsInfo.setProductionEn(icrVehicleRespVO.getProductionEn());
            }
        }
    }

    /**
     * 处理 BG 类型订单的信息组装
     *
     * @param orderIntegrationRespVO 响应对象，用于装载 BG 商品信息
     * @param orderCode              订单编号
     * @param orderInfoDO            当前订单信息
     */
    private void handleBrandGoodsOrder(OrderIntegrationRespVO orderIntegrationRespVO, String orderCode, OrderInfoDO orderInfoDO) {
        List<OrderItemDO> brandGoodsOrderItemList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .in(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getIsDeleted, 0)
                .orderByAsc(OrderItemDO::getId));

        if (CollUtil.isEmpty(brandGoodsOrderItemList)) {
            return;
        }

        BgInfo bgInfo = new BgInfo();
        OrderItemDO firstItem = brandGoodsOrderItemList.get(0);
        bgInfo.setProductName(firstItem.getProductName());
        bgInfo.setProductImageUrl(firstItem.getProductImageUrl());
        bgInfo.setProductAttribute(AttributeUtil.formatProductAttributes(firstItem.getProductAttribute()));
        bgInfo.setCostAmount(MoneyUtil.convertFromCents(new BigDecimal(orderInfoDO.getCostAmount())));
        bgInfo.setCostPoints(orderInfoDO.getPointAmount());

        int totalQuantity = brandGoodsOrderItemList.stream()
                .mapToInt(OrderItemDO::getProductQuantity)
                .sum();
        bgInfo.setProductTotalQuantity(String.valueOf(totalQuantity));

        // 获取商品快照信息
        Map<String, ProductSnapshotDTO> productSnapshotMap = getProductSnapShotMap(
                brandGoodsOrderItemList.stream()
                        .map(OrderItemDO::getProductVersionCode)
                        .collect(Collectors.toList()));

        // 构建每个商品行信息
        List<BgGood> goodList = brandGoodsOrderItemList.stream()
                .map(orderItem -> buildBgGood(orderItem, productSnapshotMap))
                .collect(Collectors.toList());

        bgInfo.setGoodList(goodList);
        orderIntegrationRespVO.setBgInfo(bgInfo);
    }

    /**
     * 构建单个 BG 商品行信息
     *
     * @param orderItem         订单商品项
     * @param productSnapshotMap 商品快照映射表
     * @return 组装好的 BgGood 对象
     */
    private BgGood buildBgGood(OrderItemDO orderItem, Map<String, ProductSnapshotDTO> productSnapshotMap) {
        BgGood bgGood = new BgGood();
        bgGood.setProductName(orderItem.getProductName());
        bgGood.setProductAttribute(AttributeUtil.formatProductAttributes(orderItem.getProductAttribute()));
        bgGood.setProductImageUrl(orderItem.getProductImageUrl());
        bgGood.setProductQuantity(String.valueOf(orderItem.getProductQuantity()));
        bgGood.setSalePrice(MoneyUtil.convertFromCents(new BigDecimal(orderItem.getCostAmount())));

        Integer pointAmount = Optional.ofNullable(orderItem.getPointAmount()).orElse(0);
        bgGood.setPoint(pointAmount / Math.max(orderItem.getProductQuantity(), 1));

        ProductSnapshotDTO productSnapshot = productSnapshotMap.get(orderItem.getProductVersionCode());
        if (productSnapshot != null) {
            bgGood.setCategoryCodeLevel1Name(productSnapshot.getCategoryCodeLevel1Name());
            bgGood.setCategoryCodeLevel2Name(productSnapshot.getCategoryCodeLevel2Name());
        }

        return bgGood;
    }

    @Override
    public OrderIntegrationLatestOrderRespVO getLatestOrders(OrderLatestListReqDTO dto) {
        OrderIntegrationLatestOrderRespVO orderIntegrationLatestOrderRespVO = new OrderIntegrationLatestOrderRespVO();

        fillPhoneNumber(dto, orderIntegrationLatestOrderRespVO);

        // 构建分页
        Page<OrderInfoDO> page = new Page<>(dto.getPageNo(), dto.getPageSize());

        // 获取当前时间并减去3年
        Date threeYearsAgo = DateUtils.addYears(new Date(), -3);

        String businessCode = null;
        List<String> codes = new ArrayList<>();
        fillCodes(dto, codes);

        // 构建查询条件
        LambdaQueryWrapper<OrderInfoDO> queryWrapper = new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getConsumerCode, dto.getJlrId())
                .eqIfPresent(OrderInfoDO::getOrderChannel, dto.getChannelCode())
                .eq(OrderInfoDO::getIsDeleted, 0)
                .inIfPresent(OrderInfoDO::getBusinessCode,codes)
                .ne(OrderInfoDO::getOrderType, OrderTypeEnum.PARENT.getCode())
                // 添加创建时间大于等于三年前的时间条件
                .ge(OrderInfoDO::getCreatedTime, threeYearsAgo);
        assembleOrderBy(dto, queryWrapper);

        // 执行查询
        page = orderInfoDOMapper.selectPage(page, queryWrapper);

        // 获取records，将records中的 orderCode 收集起来为一个list
        List<OrderInfoDO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return orderIntegrationLatestOrderRespVO;
        }

        List<String> allOrderCodeList = records.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
        List<OrderItemDO> bundlesOrderItemDOList = new ArrayList<>();
        List<String> carVinList = new ArrayList<>();
        List<IcrVehicleListRespVO> icrVehicleList = new ArrayList<>();
        List<VCSOrderInfoDO> vcsOrderInfoDOList = new ArrayList<>();
        if (BusinessIdEnum.VCS.getName().equals(dto.getBusinessName())){
        // 通过orderCodes从t_order_item表中获取对应的OrderItem对象, 比如item对象中包括商品信息 ； 再把List<OrderItem> 转换为map结构，key为OrderCode，value为List<OrderItem>对象
        //查询组合商品orderCode
        List<String> bundlesOrderCodeList = records.stream()
                .filter(orderInfoDO -> orderInfoDO.getOrderType().equals(OrderTypeEnum.BUNDLED_GOODS.getCode()))
                .map(OrderInfoDO::getOrderCode).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(bundlesOrderCodeList)) {
            //组合商品item信息
            bundlesOrderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                    .in(OrderItemDO::getOrderCode, bundlesOrderCodeList)
                    .eq(OrderItemDO::getOrderItemSpuType, OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode())
                    .eq(OrderItemDO::getIsDeleted, 0)
            );
        }

        //查询普通商品orderCode
        List<String> normalOrderCodeList = records.stream()
                .filter(orderInfoDO -> orderInfoDO.getOrderType().equals(OrderTypeEnum.PIVI.getCode())
                        || orderInfoDO.getOrderType().equals(OrderTypeEnum.VCS.getCode()))
                .map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(normalOrderCodeList)) {
            List<OrderItemDO> normalOrderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                    .in(OrderItemDO::getOrderCode, normalOrderCodeList)
                    .eq(OrderItemDO::getOrderItemSpuType, OrderItemSpuTypeEnum.NORMAL_GOOD.getCode())
                    .eq(OrderItemDO::getIsDeleted, 0));
            bundlesOrderItemDOList.addAll(normalOrderItemDOList);

        }
             vcsOrderInfoDOList = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                .in(VCSOrderInfoDO::getOrderCode, allOrderCodeList)
                .in(BaseDO::getIsDeleted, false));

        if (CollUtil.isNotEmpty(vcsOrderInfoDOList)) {
            carVinList = vcsOrderInfoDOList.stream().map(
                    vo -> piplDataUtil.getDecodeText(vo.getCarVin())).distinct().collect(Collectors.toList());
                CommonResult<List<IcrVehicleListRespVO>> listCommonResult = icrVehicleApi.vinInfo(carVinList);
                icrVehicleList = listCommonResult.getData();
            }
        } else {
            if(BusinessIdEnum.BRAND_GOODS.getCode().equals(dto.getBusinessName())){
                List<OrderItemDO> normalOrderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                        .in(OrderItemDO::getOrderCode, allOrderCodeList)
                        .eq(OrderItemDO::getIsDeleted, 0));
                bundlesOrderItemDOList.addAll(normalOrderItemDOList);
            }
        }

        Map<String, List<OrderItemDO>> orderItemMap = null;
        if (CollUtil.isNotEmpty(bundlesOrderItemDOList)) {
            orderItemMap = bundlesOrderItemDOList.stream().collect(Collectors.groupingBy(OrderItemDO::getOrderCode));
        }

        // 转换为VO
        Map<String, List<OrderItemDO>> finalOrderItemMap = orderItemMap;

        List<OrderIntegrationPageRespVO> respVOList = getOrderIntegrationPageRespVOS(records, vcsOrderInfoDOList, icrVehicleList, finalOrderItemMap);

        PageResult<OrderIntegrationPageRespVO> pageResult = new PageResult<>(respVOList, page.getTotal());
        orderIntegrationLatestOrderRespVO.setPageResult(pageResult);
        return orderIntegrationLatestOrderRespVO;
    }

    private void assembleOrderBy(OrderLatestListReqDTO dto, LambdaQueryWrapper<OrderInfoDO> queryWrapper) {
        if (Objects.nonNull(dto.getCreatedTimeSort())) {
            boolean isAscending = SortOrder.ASCENDING.getCode().equals(dto.getCreatedTimeSort());
            queryWrapper.orderBy(true, isAscending, OrderInfoDO::getCreatedTime);
        } else {
            queryWrapper.orderByDesc(OrderInfoDO::getCreatedTime);
        }
    }

    private void fillPhoneNumber(OrderLatestListReqDTO dto, OrderIntegrationLatestOrderRespVO orderIntegrationLatestOrderRespVO) {

        CommonResult<ConsumerInfoDTO> consumerByCode = null;
        try {
            consumerByCode = consumerApi.getConsumerByCode(dto.getJlrId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (consumerByCode != null && consumerByCode.getData() != null) {
            String phoneEncrypt = consumerByCode.getData().getPhoneEncrypt();
            String phone = phoneNumberDecodeUtil.getDecodePhone(phoneEncrypt);
            orderIntegrationLatestOrderRespVO.setPhoneNumber(phone);
        }
    }

    private void fillCodes(OrderLatestListReqDTO dto, List<String> codes) {
        String businessCode;
        if(StringUtils.isNotBlank(dto.getBusinessName())){
            if(BusinessIdEnum.VCS.getName().equals(dto.getBusinessName())){
                businessCode = BusinessIdEnum.VCS.getCode();
                codes.add(businessCode);
            }else {
                codes.add(BusinessIdEnum.BRAND_GOODS.getCode());
                codes.add(BusinessIdEnum.LRE.getCode());
            }
        }
    }

    private List<OrderIntegrationPageRespVO> getOrderIntegrationPageRespVOS(List<OrderInfoDO> records, List<VCSOrderInfoDO> vcsOrderInfoDOList, List<IcrVehicleListRespVO> icrVehicleList, Map<String, List<OrderItemDO>> finalOrderItemMap) {
        List<OrderPaymentRecordsDO> payList = orderPaymentRecordsMapper.selectList(new LambdaQueryWrapper<OrderPaymentRecordsDO>()
                .in(OrderPaymentRecordsDO::getOrderCode, records.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList()))
                .eq(OrderPaymentRecordsDO::getIsDeleted, 0)
                .orderByDesc(OrderPaymentRecordsDO::getCreatedTime));
        return records.stream().map(item -> {
            OrderIntegrationPageRespVO vo = new OrderIntegrationPageRespVO();

            fillOrderBasicInfo(vcsOrderInfoDOList, icrVehicleList, item, vo, payList);

            // 复制一张订单下 多个商品信息值
            String currentOrderCode = item.getOrderCode();
            if (finalOrderItemMap != null && finalOrderItemMap.containsKey(currentOrderCode)) {
                List<OrderItemDO> curOrderItemDOList = finalOrderItemMap.get(currentOrderCode);
                //统计curOrderItemDOList里面的商品数量
                vo.setTotalProductNum(curOrderItemDOList.stream().mapToInt(OrderItemDO::getProductQuantity).sum());
                vo.setProductItemInfo(curOrderItemDOList.stream().map(curOrderItemDO -> {
                    OrderIntegrationPageRespVO.ProductItemInfoData productItemInfoData = new OrderIntegrationPageRespVO.ProductItemInfoData();

                    // 复制商品信息属性值
                    BeanUtils.copyProperties(curOrderItemDO, productItemInfoData);

                    productItemInfoData.setProductCode(curOrderItemDO.getProductCode());
                    // 设置一些需要处理的属性
                    productItemInfoData.setProductAttribute(AttributeUtil.formatProductAttributes(curOrderItemDO.getProductAttribute()));
                    if (curOrderItemDO.getProductMarketPrice() != null) {
                        productItemInfoData.setProductMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(curOrderItemDO.getProductMarketPrice())));
                    }
                    productItemInfoData.setProductSalePrice(MoneyUtil.convertFromCents(new BigDecimal(curOrderItemDO.getProductSalePrice())));
                    productItemInfoData.setTotalAmount(curOrderItemDO.getTotalAmount()!=null?MoneyUtil.convertFromCents(new BigDecimal(curOrderItemDO.getTotalAmount())):"");

                    handleAmountAndPoint(productItemInfoData,curOrderItemDO);
                    return productItemInfoData;
                }).collect(Collectors.toList()));
            }

            return vo;
        }).collect(Collectors.toList());
    }

    private void fillOrderBasicInfo(List<VCSOrderInfoDO> vcsOrderInfoDOList,
                                    List<IcrVehicleListRespVO> icrVehicleList,
                                    OrderInfoDO item,
                                    OrderIntegrationPageRespVO vo,
                                    List<OrderPaymentRecordsDO> payList) {
        // 复制订单基本属性值
        vo.setOrderCode(item.getOrderCode());
        vo.setCarVin("");
        if(CollUtil.isNotEmpty(vcsOrderInfoDOList)) {
            VCSOrderInfoDO vcsOrderInfoDO = vcsOrderInfoDOList.stream().filter(vcs -> vcs.getOrderCode().equals(vo.getOrderCode())).findFirst().orElse(new VCSOrderInfoDO());
            vo.setCarVin(piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin()));
        }
        vo.setCreatedTime(item.getOrderTime());
        if(CollUtil.isNotEmpty(icrVehicleList)){
            IcrVehicleListRespVO vehicleListRespVO = icrVehicleList.stream().filter(carInfo -> carInfo.getCarVin().equals(vo.getCarVin())).findFirst().orElse(null);
            vo.setVehicleRespVO(vehicleListRespVO);
        }
        vo.setDiscountTotalAmount(item.getDiscountTotalAmount()==null?null :MoneyUtil.convertFromCents(new BigDecimal(item.getDiscountTotalAmount())));
        vo.setFeeTotalAmount(item.getFeeTotalAmount() ==null ?null : MoneyUtil.convertFromCents(new BigDecimal(item.getFeeTotalAmount())));
        vo.setPointAmount(item.getPointAmount() ==null ? "" : String.valueOf(item.getPointAmount()));
        vo.setCostAmount(MoneyUtil.convertFromCents(new BigDecimal(item.getCostAmount())));
        vo.setPaymentTime(payList.stream().filter(pay -> pay.getOrderCode().equals(item.getOrderCode())).findFirst().orElse(new OrderPaymentRecordsDO()).getSubmitTime());
    }

    private void handleAmountAndPoint(OrderIntegrationPageRespVO.ProductItemInfoData productItemInfoData, OrderItemDO curOrderItemDO) {
        //设置单个消费金额 costAmount/productQuantity
        BigDecimal costPerItem = BigDecimal.ZERO;
        if (curOrderItemDO.getCostAmount() != null && curOrderItemDO.getProductQuantity() != 0) {
            productItemInfoData.setTotalCostAmount(MoneyUtil.convertFromCents(new BigDecimal(curOrderItemDO.getCostAmount())));
            costPerItem = new BigDecimal(curOrderItemDO.getCostAmount())
                    .divide(BigDecimal.valueOf(curOrderItemDO.getProductQuantity()), 2, RoundingMode.HALF_UP);
        }
        productItemInfoData.setCostAmount(MoneyUtil.convertFromCents(costPerItem));

        // 处理积分计算
        String pointPerItem = "";
        if (curOrderItemDO.getPointAmount() != null && curOrderItemDO.getProductQuantity() != 0) {
            long points = curOrderItemDO.getPointAmount() / curOrderItemDO.getProductQuantity();
            pointPerItem = String.valueOf(points);
            productItemInfoData.setTotalPointAmount(String.valueOf(curOrderItemDO.getPointAmount()));
        }
        productItemInfoData.setPointAmount(pointPerItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateOrderStatus(String orderCode) {
        log.info("支付后订单状态更新开始，入参：orderCode={}", orderCode);

        // 存储需要更新的订单信息
        List<OrderInfoDO> updateOrderInfos = new ArrayList<>();
        List<OrderStatusLogDO> orderStatusLogs = new ArrayList<>();

        // 查询orderCode作为父订单下的所有子订单
        List<OrderInfoDO> childOrders = orderInfoDOMapper.selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                        .eq(BaseDO::getIsDeleted, false)
                        .eq(OrderInfoDO::getParentOrderCode, orderCode)
                // 去除父订单
//                .ne(OrderInfoDO::getOrderType, 0)
        );

        // 如果父订单代码不为空，则查询所有未删除且与该父订单代码匹配的子订单
        if (CollUtil.isNotEmpty(childOrders)) {
            log.info("支付后订单状态更新：找到关联子订单列表: {}", JSON.toJSONString(childOrders));

            for (OrderInfoDO orderInfoDO : childOrders) {
                // 对每个子订单进行支付后的状态更新操作
                processOrderAfterPayment(orderInfoDO, updateOrderInfos, orderStatusLogs);
            }
        }

        // 批量更新订单信息
        if (CollUtil.isNotEmpty(updateOrderInfos)) {
            orderInfoDOMapper.updateBatch(updateOrderInfos);
        }

        // 批量插入订单状态日志
        if (CollUtil.isNotEmpty(orderStatusLogs)) {
            orderStatusLogDOMapper.insertBatch(orderStatusLogs);
        }

        log.info("支付后订单状态更新结束");
        // 返回成功标识（可根据实际业务需求定义不同的返回码）
        return 0;
    }

    @Override
    public Integer updateOrderStatusOnSuccess(String orderCode, String payFinishTime) {
        log.info("支付成功后订单状态更新开始,入参: orderCode={}", orderCode);
        boolean checkRes = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent("updateOrderStatusOnSuccess:" + orderCode, "1", 1, TimeUnit.MINUTES));
        if (!checkRes) {
            log.info("updateOrderStatusOnSuccess code重复回调:{}", orderCode);
            return 0;
        }

        // 查询订单基本信息
        OrderInfoDO orderInfo = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, false)
        );

        if (Objects.isNull(orderInfo)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }

        //判断支付状态 业务上幂等
        if (PaymentStatusEnum.PAID.getCode().equals(orderInfo.getPaymentStatus())) {
            return 0;
        }

        handleIsPayDown(orderInfo);

        // 存储需要更新的订单信息
        List<OrderInfoDO> updateOrderInfos = new ArrayList<>();
        List<OrderStatusLogDO> orderStatusLogs = new ArrayList<>();
        //判断是父单还是子单 ordertype大于0是子单
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfo.getOrderType())) {
            // 查询orderCode作为父订单下的所有子订单
            List<OrderInfoDO> childOrders = orderInfoDOMapper.selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                            .eq(BaseDO::getIsDeleted, false)
                            .eq(OrderInfoDO::getParentOrderCode, orderCode)
                    // 去除父订单
//                .ne(OrderInfoDO::getOrderType, 0)
            );

            if (CollUtil.isNotEmpty(childOrders)) {
                log.info("支付成功后订单状态更新：找到关联子订单列表: {}", JSON.toJSONString(childOrders));

                for (OrderInfoDO orderInfoDO : childOrders) {
                    // 对每个子订单进行支付成功的状态更新操作
                    processOrderAfterPaymentSuccess(orderInfoDO, updateOrderInfos, orderStatusLogs, payFinishTime);
                }
            }
        } else {
            // 对每个子订单进行支付成功的状态更新操作
            processOrderAfterPaymentSuccess(orderInfo, updateOrderInfos, orderStatusLogs, payFinishTime);
        }

        executeUpdateAndSendKafka(updateOrderInfos, orderStatusLogs);
        return 0;
    }

    private void handleIsPayDown(OrderInfoDO orderInfo) {

        //处理redisKey 修改支付回调状态是支付完成
        //1.Constants.SUCCESS_PAYING_ORDER_KEY + orderInfoDO.getOrderCode(); 查询是否存在
        String success_paying_order = Constants.SUCCESS_PAYING_ORDER_KEY + orderInfo.getOrderCode();
        String success_paying_parent = Constants.SUCCESS_PAYING_ORDER_KEY + orderInfo.getParentOrderCode();
        try {
            Boolean orderBoolean = redisTemplate.hasKey(success_paying_order);
            Boolean parentBoolean = redisTemplate.hasKey(success_paying_parent);

            if (Boolean.TRUE.equals(orderBoolean)) {
                //修改对应的value值为completed 不修改过期时间
                redisTemplate.opsForValue().set(success_paying_order, OrderPaymentCallBackEnum.COMPLETED.getCode(),5,TimeUnit.SECONDS);

                log.info("设置订单支付回调状态 redisKey:{},value:{}", success_paying_order, OrderPaymentCallBackEnum.COMPLETED.getCode());
            }

            if (Boolean.TRUE.equals(parentBoolean)) {
                //修改对应的value值为completed 不修改过期时间
                redisTemplate.opsForValue().set(success_paying_parent, OrderPaymentCallBackEnum.COMPLETED.getCode(),5,TimeUnit.SECONDS);

                log.info("设置订单支付回调状态 redisKey:{},value:{}", success_paying_parent, OrderPaymentCallBackEnum.COMPLETED.getCode());

            }
        }catch (Exception e){
            log.info("handleIsPayDown 修改redis key出错 orderCode:{}",orderInfo.getOrderCode());
        }

    }

    private void executeUpdateAndSendKafka(List<OrderInfoDO> updateOrderInfos, List<OrderStatusLogDO> orderStatusLogs) {
        transactionTemplate.executeWithoutResult((status) -> {
            // 批量更新订单信息
            if (CollUtil.isNotEmpty(updateOrderInfos)) {
                orderInfoDOMapper.updateBatch(updateOrderInfos);

                //更新订单支付记录
                updatePaymentRecords(updateOrderInfos);
            }

            // 批量插入订单状态日志
            if (CollUtil.isNotEmpty(orderStatusLogs)) {
                orderStatusLogDOMapper.insertBatch(orderStatusLogs);
            }

            // 注册事务同步器，确保事务提交后执行
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                    // 事务提交后执行的代码，LRE支付成功发放券
                    for (OrderInfoDO updateOrderInfo: updateOrderInfos) {
                        sendMsgAndKafka(updateOrderInfo);
                    }
                }
            });
        });
    }

    private void sendMsgAndKafka(OrderInfoDO updateOrderInfo) {
        if (OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(updateOrderInfo.getOrderType())) {
            sendOrderECouponSuccessfulMessage(updateOrderInfo);
        }

        // VCS发送短信逻辑
        if (BusinessIdEnum.VCS.getCode().equals(updateOrderInfo.getBusinessCode())) {
            // 只有非聚合父订单才发送短信
            if (!OrderTypeEnum.PARENT.getCode().equals(updateOrderInfo.getOrderType())) {
                // send kafka msg
                sendKafka(updateOrderInfo);
            }
        }
        // BG发送短信逻辑
        else if (BusinessIdEnum.BRAND_GOODS.getCode().equals(updateOrderInfo.getBusinessCode())) {
            // 精选好物订单支付成功通知
            sendBgMsg(updateOrderInfo);
        }
    }

    /**
     * 精选好物订单支付成功通知
     */
    private void sendBgMsg(OrderInfoDO orderInfo) {
        // 解密手机号
        String phone = phoneNumberDecodeUtil.getDecodePhone(orderInfo.getContactPhone());
        String wxUrl = shortLinkHandler.getBGOrderDetailShortLink(orderInfo.getOrderCode());
        notificationProducer.sendPaymentSuccessNotification(orderInfo.getOrderCode(), phone, wxUrl);
    }

    /**
     * 发送券发放消息到Kafka
     *
     * @param orderInfo 订单信息
     */
    private void sendOrderECouponSuccessfulMessage(OrderInfoDO orderInfo) {
        try {
            log.info("开始发送支付成功后券发放消息，orderCode:{}", orderInfo.getOrderCode());

            List<OrderItemDO> orderItems = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                    .eq(OrderItemDO::getOrderCode, orderInfo.getOrderCode())
                    .eq(OrderItemDO::getIsDeleted, false)
            );

            List<OrderECouponSuccessfulMessage.OrderItem> orderItemMQs = new ArrayList<>();
            for (OrderItemDO orderItem:orderItems) {
                OrderECouponSuccessfulMessage.OrderItem orderItemMQ = new OrderECouponSuccessfulMessage.OrderItem();
                orderItemMQ.setOrderItemCode(orderItem.getOrderItemCode());
                orderItemMQ.setCouponModelCode(orderItem.getCouponModelCode());
                orderItemMQ.setProductQuantity(orderItem.getProductQuantity());
                orderItemMQs.add(orderItemMQ);
            }

            // 构建消息体
            OrderECouponSuccessfulMessage orderECouponSuccessfulMessage = OrderECouponSuccessfulMessage.builder()
                    .messageId(ecpIdUtil.nextIdStr())
                    .tenantId(TenantContextHolder.getTenantId())
                    .consumerCode(orderInfo.getConsumerCode())
                    .orderCode(orderInfo.getOrderCode())
                    .orderItems(orderItemMQs)
                    .build();

            // 发送消息到Kafka
            producerTool.sendMsg(KafkaConstants.ORDER_ECOUPON_SUCCESSFUL_TOPIC, "",  JSON.toJSONString(orderECouponSuccessfulMessage));

            log.info("券发放消息发送成功，orderCode:{}, message:{}",
                    orderInfo.getOrderCode(), JSON.toJSONString(orderECouponSuccessfulMessage));
        } catch (Exception e) {
            log.error("券发放消息发送失败，orderCode:{}, error:{}", orderInfo.getOrderCode(), e.getMessage(), e);
            // 这里只记录日志，不影响主流程
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayOrderRespVO buildPaymentRequest(PayRequestDTO inputDto) {

        log.info("buildPaymentRequest方法开始构建支付请求，inputDto:{}", JSON.toJSONString(inputDto));

        // 查询订单基本信息
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, inputDto.getOrderCode())
                .eq(OrderInfoDO::getIsDeleted, false)
        );

        if (Objects.isNull(orderInfoDO)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }

        // 校验订单是否可支付 （需要订单状态处于已下单 支付状态处于待支付）
        if (!orderInfoDO.getOrderStatus().equals(OrderStatusEnum.ORDERED.getCode()) || !orderInfoDO.getPaymentStatus().equals(PaymentStatusEnum.TO_BE_PAID.getCode())) {
            throw exception(ErrorCodeConstants.ORDER_NOT_PAYABLE);
        }

        //代客下单的客户留言
        if(OrderChannelCodeEnum.CUSTOMER_SERVICE.getOrderChannelCode().equals(orderInfoDO.getOrderChannel())){
            orderInfoDO.setCustomerRemark(inputDto.getOperatorRemark());
            orderInfoDOMapper.updateById(orderInfoDO);
        }

        // 处理赠品信息校验
        handleGiftInfoValidation(inputDto);

        // 设置订单号、支付金额
        SubmitPayOrderReq submitPayOrderReq = new SubmitPayOrderReq();

        // 设置支付金额
        Long payAmount = Optional.ofNullable(orderInfoDO.getCostAmount()).map(Long::valueOf).orElse(0L);
        if (Objects.nonNull(orderInfoDO.getFreightAmount())) {
            payAmount += orderInfoDO.getFreightAmount();
        }
        submitPayOrderReq.setPayAmount(payAmount);

        // 设置业务订单号
        submitPayOrderReq.setOrderNo(orderInfoDO.getOrderCode());

        // 设置 JLR-ID
        submitPayOrderReq.setJlrid(orderInfoDO.getConsumerCode());

        // 设置支付方式
        submitPayOrderReq.setPayMethod(PayMethodEnum.WXPAY.getCode());

        // 设置支付渠道 (根据订单 Business Code 区分)
        String businessCode = orderInfoDO.getBusinessCode();
        log.info("buildPaymentRequest获取到的支付Business Code:{}", businessCode);
        if (businessCode != null &&
                businessCode.equals(BusinessIdEnum.VCS.getCode())) {
            // 如果是 VCS 商品则使用 CUSC-LANDROVER 支付渠道
            submitPayOrderReq.setChannelCode(ChannelCodeEnum.CUSC_LANDROVER.getCode());
            log.info("根据 Business Code 判断支付渠道为 VCS");
        } else {
            // 否则使用 HUIFU 进行支付
            submitPayOrderReq.setChannelCode(ChannelCodeEnum.HUIFU.getCode());
            log.info("根据 Business Code 判断支付渠道为 HUIFU");
        }

        // 设置支付渠道的额外参数
        submitPayOrderReq.setChannelExtras(new HashMap<>());

        List<OrderInfoDO> childOrders = new ArrayList<>();

        // 如果是 VCS 商品则按照 VCS 的逻辑处理 Order Items
        if (null != businessCode && businessCode.equals(BusinessIdEnum.VCS.getCode())) {
            // 处理 VCS 请求
            childOrders = processVcsOrderRequest(submitPayOrderReq, inputDto, orderInfoDO);
        } else {
            processNonVcsOrderItem(submitPayOrderReq, inputDto, orderInfoDO);
            //父单
            if (Objects.equals(orderInfoDO.getOrderType(), 0)) {
                childOrders = orderInfoDOMapper.selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                        .eq(OrderInfoDO::getParentOrderCode, orderInfoDO.getOrderCode())
                        .eq(OrderInfoDO::getIsDeleted, false)
                        .orderByAsc(OrderInfoDO::getId)
                );
                childOrders.add(orderInfoDO);
            } else {
                //非父单
                childOrders.add(orderInfoDO);
            }
        }

        return executePaymentPayOrder(orderInfoDO, submitPayOrderReq, childOrders);
    }

    private void handleGiftInfoValidation(PayRequestDTO inputDto) {
        log.info("handleGiftInfoValidation开始处理赠品信息校验，inputDto:{}", JSON.toJSONString(inputDto));

        // 如果 giftInfoDTO 为 null，手动设置 needGift 为 2
        if (inputDto.getGiftInfoDTO() == null) {
            OrderGiftAddressDTO defaultGiftInfo = new OrderGiftAddressDTO();
            defaultGiftInfo.setNeedGift(GiftAddressEnum.NOT_REQUIRED.getCode()); // 设置 needGift 为 2
            inputDto.setGiftInfoDTO(defaultGiftInfo);
        }
        log.info("handleGiftInfoValidation处理后的 inputDto:{}", JSON.toJSONString(inputDto));

        // 校验 needGift 是否有效
        if (EnumUtil.getBy(GiftAddressEnum::getCode, inputDto.getGiftInfoDTO().getNeedGift()) == null) {
            throw exception(GIFT_ADDRESS_INVALID);
        }

        // 校验收货地址参数
        validate(inputDto.getGiftInfoDTO());
        log.info("handleGiftInfoValidation.validate处理后的 inputDto:{}", JSON.toJSONString(inputDto));
    }

    /**
     * 处理非 VCS OrderItem
     *
     * @param submitPayOrderReq SubmitPayOrderReq
     * @param inputDto PayRequestDTO
     * @param orderInfo OrderInfoDO
     * @return Boolean of isZeroPay
     */
    public void processNonVcsOrderItem(SubmitPayOrderReq submitPayOrderReq,
                                       PayRequestDTO inputDto,
                                       OrderInfoDO orderInfo) {
        final String openIdField = "openId";
        final String miniAppIdField = "miniAppId";

        log.info("processNonVcsOrderItem 非 VCS 订单，处理逻辑");

        // Set OpenId & miniAppId
        submitPayOrderReq.getChannelExtras().put(openIdField, inputDto.getOpenid());
        submitPayOrderReq.getChannelExtras().put(miniAppIdField, inputDto.getMiniAppId());

        // 查询子单
        List<OrderInfoDO> childOrders = null;
        if (orderInfo.getOrderType().equals(0)) {
            childOrders = orderInfoDOMapper.listChildOrder(orderInfo.getOrderCode());
        }

        Set<String> payTypeSet = new HashSet<>();
        List<SubmitPayOrderReq.OrderItem> orderItemList = submitPayOrderComponent.processToOrderItemList(orderInfo, childOrders, payTypeSet);
        if (CollUtil.isNotEmpty(childOrders)) {
            List<OrderInfoDO> allOrders = Lists.newArrayList(orderInfo);
            allOrders.addAll(childOrders);
            submitPayOrderReq.setOrderTitle(getOrderTitle(allOrders));
        } else {
            submitPayOrderReq.setOrderTitle(getOrderTitle(List.of(orderInfo)));
        }

        // 处理 PayType
        // 如果 Set<PayType>.size() == 1 则为当前 PayType
        if (payTypeSet.size() == 1) {
            submitPayOrderReq.setPayType(payTypeSet.iterator().next());
        }

        // 如果支付方式 > 1
        if (payTypeSet.size() > 1) {
            submitPayOrderReq.setPayType(PayTypeEnum.COMBINE.getCode());
        }

        log.info("processNonVcsOrderItem OrderItemList: {}", JSON.toJSONString(orderItemList));
        submitPayOrderReq.setOrderItems(orderItemList);
    }

    /**
     * 处理 VCS Request
     *
     * @param submitPayOrderReq SubmitPayOrderReq
     * @param inputDto          PayRequestDTO
     * @param orderInfoDO       OrderInfoDO
     */
    public List<OrderInfoDO> processVcsOrderRequest(SubmitPayOrderReq submitPayOrderReq,
                                                    PayRequestDTO inputDto,
                                                    OrderInfoDO orderInfoDO) {

        // 设置支付类型
        submitPayOrderReq.setPayType(PayTypeEnum.CASH.getCode());

        // Set ExtraId
        submitPayOrderReq.getChannelExtras().put("openId", inputDto.getOpenid());

        log.info("VCS 商品进入 VCS 订单处理逻辑");

        List<OrderInfoDO> childOrders = new ArrayList<>();

        // 组合支付时，若无明确子单号, 则取第一个子订单的 ProductCode 和 OrderTitle
        if (orderInfoDO.getOrderType() == 0) {

            // 查询子订单
            childOrders = orderInfoDOMapper.selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                    .eq(OrderInfoDO::getParentOrderCode, orderInfoDO.getOrderCode())
                    .eq(OrderInfoDO::getIsDeleted, false)
                    .orderByAsc(OrderInfoDO::getId)
            );

            // 按照ID升序排列, 通常第一项为最先加入购物车的商品
            // 过滤orderType不为0的第一项childOrders
            List<OrderInfoDO> infoDOList = childOrders.stream().filter(orderInfoDO1 -> !orderInfoDO1.getOrderType().equals(0)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(childOrders)) {
                OrderInfoDO childOrder = infoDOList.get(0);
                String childOrderOrderCode = childOrder.getOrderCode();
                OrderItemDO firstSubOrderItem = orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                        .eq(OrderItemDO::getOrderCode, childOrderOrderCode)
                        .eq(OrderItemDO::getIsDeleted, false)
                        .last(Constants.LIMIT_ONE)
                );
                SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();

                orderItem.setPayAmount(Long.valueOf(orderInfoDO.getCostAmount()));
                orderItem.setProductNo(firstSubOrderItem.getProductCode());
                orderItem.setProductName(firstSubOrderItem.getProductName());
                orderItem.setItemType(PayTypeEnum.CASH.getCode());

                // 设置 Order Item
                submitPayOrderReq.setOrderItems(List.of(orderItem));

                // 设置订单标题 (OrderCode)
                submitPayOrderReq.setOrderTitle(firstSubOrderItem.getProductName());
            }
        } else {

            // 若非组合支付, 则直接取订单本身的 ProductCode 和 OrderTitle
            // 根据 OrderItemSpuType 倒排, 取第一个虚拟组合类型默认取虚拟组合商品本身
            OrderItemDO orderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                    .eq(OrderItemDO::getOrderCode, orderInfoDO.getOrderCode())
                    .eq(OrderItemDO::getIsDeleted, false)
                    .orderByDesc(OrderItemDO::getOrderItemSpuType)
                    .last(Constants.LIMIT_ONE));
            SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();

            orderItem.setPayAmount(Long.valueOf(orderInfoDO.getCostAmount()));
            orderItem.setProductNo(orderItemDO.getProductCode());
            orderItem.setProductName(orderItemDO.getProductName());
            orderItem.setItemType(PayTypeEnum.CASH.getCode());

            submitPayOrderReq.setOrderItems(List.of(orderItem));
            // 设置订单标题 (OrderCode)
            submitPayOrderReq.setOrderTitle(orderItemDO.getProductName());
            childOrders.add(orderInfoDO);
            // 代客下单 在等待支付页面 可配置地址, 代客下单此时都是子单的维度进行支付
            if(orderInfoDO.getOrderChannel().equals(CUSTOMER_SERVICE.getOrderChannelCode())){
                orderGiftAddressService.saveOrderGiftAddressForValet(inputDto.getGiftInfoDTO(), List.of(inputDto.getOrderCode()));
            }
        }
        return childOrders;
    }

    private String getOrderTitle(List<OrderInfoDO> orders) {
        Set<String> businessCodes = orders.stream().filter(order -> StrUtil.isNotEmpty(order.getBusinessCode()))
                .map(OrderInfoDO::getBusinessCode).collect(Collectors.toSet());
        return OrderTitleUtil.getOrderTitle(businessCodes);
    }

    private PayOrderRespVO executePaymentPayOrder(OrderInfoDO orderInfoDO,
                                                  SubmitPayOrderReq submitPayOrderReq,
                                                  List<OrderInfoDO> childOrders) {
        log.info("[executePaymentPayOrder] costAmount: {}, freightAmount: {}, payAmount: {}",
                orderInfoDO.getCostAmount(), orderInfoDO.getFreightAmount(), submitPayOrderReq.getPayAmount());
        Boolean isZeroPay = (Objects.isNull(submitPayOrderReq.getPayAmount())
                    || Objects.equals(submitPayOrderReq.getPayAmount().intValue(), 0))
                ? Boolean.TRUE : Boolean.FALSE;
        log.info("[executePaymentPayOrder] 参数: orderInfoDO： {}, submitPayOrderReq: {}, childOrders: {}, isZeroPay: {}",
                JsonUtils.toJsonString(orderInfoDO), JsonUtils.toJsonString(submitPayOrderReq),
                JsonUtils.toJsonString(childOrders), isZeroPay);
        // 调取 payment-service api
        CommonResult<SubmitPayOrderResp> payOrderSubmitRespVOCommonResult = null;
        PayOrderRespVO payOrderRespVO = new PayOrderRespVO();
        try {
            log.info("[executePaymentPayOrder] 请求 payment service: {}", JsonUtils.toJsonString(submitPayOrderReq));
            payOrderSubmitRespVOCommonResult = payOrderApi.submitPayOrder(submitPayOrderReq);
            log.info("[executePaymentPayOrder] payment service 响应: {}", JsonUtils.toJsonString(payOrderSubmitRespVOCommonResult));

            if (Objects.isNull(payOrderSubmitRespVOCommonResult) || !payOrderSubmitRespVOCommonResult.isSuccess()) {
                log.error("[executePaymentPayOrder] request payment service failed, res: {}", JsonUtils.toJsonString(payOrderSubmitRespVOCommonResult));
                return payOrderRespVO;
            }

            // 获取支付订单提交响应
            SubmitPayOrderResp submitPayOrderResp = payOrderSubmitRespVOCommonResult.getData();

            // 如果是 0 元支付
            if (isZeroPay) {
                boolean zeroPayResult = PayOrderStatusEnum.SUCCESS.getCode().equals(submitPayOrderResp.getPayStatus());
                payOrderRespVO.setZeroPayStatus(
                        zeroPayResult ? ZeroPayStatus.SUCCESS.getStatus() : ZeroPayStatus.FAIL.getStatus());
                log.info("[executePaymentPayOrder] set Zero Pay response, orderNo: {}, zeroPayResult: {}", submitPayOrderReq.getOrderNo(), zeroPayResult);

                log.info("[executePaymentPayOrder] zero pay 插入支付记录: {}, payApplyNo: {}", JsonUtils.toJsonString(childOrders), submitPayOrderResp.getPayApplyNo());
                // 插入记录
                insertPaymentRecords(childOrders, submitPayOrderResp.getPayApplyNo());

                // 创建支付单同步返回支付成功、异步支付回调支付成功都会触发此方法
                if (zeroPayResult) {
                    String payFinishTimeStr = DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_PATTERN);
                    updateOrderStatusOnSuccess(orderInfoDO.getOrderCode(), payFinishTimeStr);
                }
            } else {
                // 判断是否可以唤起支付，当前时间-下单时间<15min
                Duration duration = Duration.between(orderInfoDO.getOrderTime(), LocalDateTime.now());
                if (submitPayOrderResp.getWxPayParamMap() != null && StringUtils.isNotBlank(submitPayOrderResp.getPayApplyNo())) {
                    log.info("[executePaymentPayOrder] set Not Zero Pay response orderNo: {}", submitPayOrderReq.getOrderNo());
                    // Set ZeroPayStatus
                    payOrderRespVO.setZeroPayStatus(ZeroPayStatus.NA.getStatus());

                    // 转换 Map<String, Object> 到 Map<String, String>
                    Map<String, String> wxPayParamMap = submitPayOrderResp.getWxPayParamMap().entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    entry -> entry.getValue().toString()
                            ));
                    payOrderRespVO.setWxpayParamMap(wxPayParamMap);
                    long dueTime = getDueTime(orderInfoDO.getBusinessCode());
                    payOrderRespVO.setInvoke(duration.toMillis() < dueTime);
                    payOrderRespVO.setCode(200);
                    payOrderRespVO.setSuccess(true);
                }
            }

            log.info("[executePaymentPayOrder] 插入支付记录: {}, payApplyNo: {}", JsonUtils.toJsonString(childOrders), submitPayOrderResp.getPayApplyNo());
            // 插入记录
            insertPaymentRecords(childOrders, submitPayOrderResp.getPayApplyNo());

        } catch (Exception e) {
            log.error("[executePaymentPayOrder] error: {}", JsonUtils.toJsonString(submitPayOrderReq), e);
        }

        log.info("[executePaymentPayOrder] response: {}", JsonUtils.toJsonString(payOrderRespVO));
        return payOrderRespVO;
    }

    private long getDueTime(String businessCode) {
        if(BusinessIdEnum.VCS.getCode().equals(businessCode)){
            return Constants.VCS_PAY_TIMEOUT_MILLS;
        }else {
            return Constants.BG_LRE_PAY_TIMEOUT_MILLS;
        }
    }

    /**
     * 插入支付记录
     *
     * @param childOrders        订单集合
     * @param payApplyNo        支付单号
     */
    private void insertPaymentRecords(List<OrderInfoDO> childOrders, String payApplyNo) {
        List<OrderPaymentRecordsDO> records = new ArrayList<>();
        for (OrderInfoDO childOrder : childOrders) {
            OrderPaymentRecordsDO orderPaymentRecordsDO = new OrderPaymentRecordsDO();
            orderPaymentRecordsDO.setOrderCode(childOrder.getOrderCode());
            orderPaymentRecordsDO.setConsumerCode(childOrder.getConsumerCode());
            orderPaymentRecordsDO.setPayStatus(PaymentStatus.PENDING.getCode());
            orderPaymentRecordsDO.setSubmitTime(LocalDateTime.now());
            orderPaymentRecordsDO.setPayApplyNo(payApplyNo);
            records.add(orderPaymentRecordsDO);
        }
        if (CollUtil.isNotEmpty(records)) {
            orderPaymentRecordsMapper.insertBatch(records);
        }
    }

    /**
     * 订单支付超时取消的job
     *
     * @param days 天数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer orderTimeoutCancel(Integer days) {
        log.info("订单支付超时取消的job, days:{}", days);
        //获取订单支付超时，且超时没有取消的订单
        List<OrderInfoDO> orderInfos = getTimeoutOrder(days);
        log.info("订单支付超时取消的job, orderInfoDOS:{}", orderInfos);

        int size = 0;
        for (OrderInfoDO orderInfo:orderInfos) {
            // 非VCS订单，只以父单维度进行支付和取消
            if (!BusinessIdEnum.VCS.getCode().equals(orderInfo.getBusinessCode())
                && !StringUtils.equals(orderInfo.getParentOrderCode(), orderInfo.getOrderCode())) {
                continue;
            }
            if (BusinessIdEnum.VCS.getCode().equals(orderInfo.getBusinessCode())
            &&  Duration.between(orderInfo.getOrderTime(), LocalDateTime.now()).toMinutes() < 270){
                continue;
            }
            CancelOrderMessage cancelOrderMessage = new CancelOrderMessage();
            cancelOrderMessage.setMessageId(UUID.randomUUID().toString().replace("-", ""));
            cancelOrderMessage.setParentOrderCode(orderInfo.getParentOrderCode());
            cancelOrderMessage.setOrderCode(orderInfo.getOrderCode());
            cancelOrderMessage.setSendTime(orderInfo.getOrderTime());
            cancelOrderMessage.setTenantId(Long.valueOf(orderInfo.getTenantId()));
            cancelOrderMessage.setBusinessCode(orderInfo.getBusinessCode());
            producerTool.sendMsg(KafkaConstants.REAL_ORDER_TIMEOUT_CANCEL_TOPIC, "", JSON.toJSONString(cancelOrderMessage));
            size++;
        }
        return size;
    }

    @Override
    public Boolean callPayment(String orderCode,String type) {
        // 查询订单下单时间
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, false)
        );
        if (Objects.isNull(orderInfoDO)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }

        if (type != null && OrderPaymentTypeEnum.SUCCESS.getType().equals(type)){
            //支付成功时候记录redis key  SUCCESS_PAYING_ORDER_KEY+orderCode
            //1.判断redis Key值是否存在，如果不存在则创建一个
            String redisKey = Constants.SUCCESS_PAYING_ORDER_KEY + orderCode;
            try {
                if (Boolean.FALSE.equals(redisTemplate.hasKey(redisKey))) {
                    redisTemplate.opsForValue().set(redisKey, OrderPaymentTypeEnum.PENDING.getType(), 5, TimeUnit.MINUTES);
                    log.info("支付成功，放redis缓存成功，支付成功订单号 redisKey:{}", redisKey);
                }
            } catch (Exception e) {
                log.info("支付成功，放redis缓存失败，支付成功订单号 redisKey:{}", redisKey);
            }
        }else {
            //支付中的时候设置redis key为支付中15分钟倒计时

            // 设置当前订单状态为 支付中
            String redisKey = Constants.PAYING_ORDER_KEY + orderCode;

            // 双倍支付时间容错
//            Duration duration = Duration.between(LocalDateTime.now(), orderInfoDO.getOrderTime());
            long delayTime = getDueTime(orderInfoDO.getBusinessCode()) * 2;
            try {
                redisTemplate.opsForValue().set(redisKey, true, delayTime, TimeUnit.MILLISECONDS);
                log.info("order服务发起支付时，放redis缓存成功，正在支付的订单号 redisKey:{}", redisKey);
            } catch (Exception e) {
                log.error("order服务发起支付时，放redis缓存失败， 正在支付的订单号 redisKey:{}", redisKey);
                throw exception(ErrorCodeConstants.PAY_ERROR);
            }
        }

        return true;
    }

    /**
     * 用户主动关闭小程序的支付密码输入框时触发，
     * @param orderCode 订单编号
     * @return
     */
    @Override
    public Boolean paymentFail(String orderCode) {
        // 移除redis 支付中的订单编号
        String redisKey = Constants.PAYING_ORDER_KEY + orderCode;
        try {
            redisTemplate.delete(redisKey);
            log.info("用户放弃支付，删除redis缓存中 正在支付的订单号 redisKey:{}", redisKey);

            // 触发订单超时自动取消逻辑，消费端会判断如果未满15分钟，则不会进行取消
            OrderInfoDO orderInfo = this.baseMapper.queryOrderDoByOrderCode(orderCode);
            CancelOrderMessage cancelOrderMessage = new CancelOrderMessage();
            cancelOrderMessage.setMessageId(UUID.randomUUID().toString().replace("-", ""));
            cancelOrderMessage.setParentOrderCode(orderInfo.getParentOrderCode());
            cancelOrderMessage.setOrderCode(orderCode);
            cancelOrderMessage.setSendTime(orderInfo.getOrderTime());
            cancelOrderMessage.setTenantId(TenantContextHolder.getTenantId());
            cancelOrderMessage.setBusinessCode(orderInfo.getBusinessCode());
            producerTool.sendMsg(KafkaConstants.REAL_ORDER_TIMEOUT_CANCEL_TOPIC, "", JSON.toJSONString(cancelOrderMessage));
            log.info("用户放弃支付，触发订单超时自动取消逻辑，mq={}", JsonUtils.toJsonString(cancelOrderMessage));
        } catch (Exception e) {
            log.error("移除redis 支付中的订单编号异常：", e);
            return false;
        } finally {
            TenantContextHolder.clear();
        }
        return true;
    }

    @Override
    public ResendMessageRespVO resendMessage(ResendMessageDTO resendMessageDTO) {
        log.info("service层 客服订单短信重发接口 入参，resendMessageDTO：{}", JSON.toJSONString(resendMessageDTO));
        String orderCode = resendMessageDTO.getOrderCode();

        // 校验订单号是否存在
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, false)
        );
        log.info("订单号：{}，查询结果：{}", orderCode, orderInfoDO);
        if(Objects.isNull(orderInfoDO)){
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        // 订单状态非已下单，无法提交重发短信请求
        if (!OrderStatusEnum.ORDERED.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw exception(ORDER_STATUS_NOT_PLACED);
        }

        // 订单来源非代客下单时，无法提交重发短信请求
        if (!OrderChannelCodeEnum.CUSTOMER_SERVICE.getOrderChannelCode().equals(orderInfoDO.getOrderChannel())) {
            throw exception(ORDER_SOURCE_NOT_GUEST);
        }

        // 订单为聚合父订单，无法提交重发短信请求 ORDER_IS_AGGREGATE
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType())) {
            throw exception(ORDER_IS_AGGREGATE);
        }

        //只有代客下单 重发短信时 更新t_customer_service_order表的recieve_phone
        CustomerServiceOrderDO customerServiceOrderDO = customerServiceOrderDOMapper.selectOne(new LambdaQueryWrapperX<CustomerServiceOrderDO>()
                .eq(CustomerServiceOrderDO::getOrderCode, orderCode)
                .eq(CustomerServiceOrderDO::getIsDeleted, false)
        );

        if (Objects.isNull(customerServiceOrderDO)) {
            log.info("订单号{}t_customer_service_order表中在没有代客下单信息", orderCode);
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS_IN_CUSTOMER_SERVICE_RELATION);
        }
        String receivePhone = resendMessageDTO.getReceivePhone();
        String messageTemplateCode = customerServiceOrderDO.getMessageTemplateCode();
        customerServiceOrderDO.setRecievePhone(receivePhone);
        customerServiceOrderDOMapper.updateById(customerServiceOrderDO);
        log.info("更新t_customer_service_order表中的recieve_phone成功，customerServiceOrderDO：{}", customerServiceOrderDO);

        //build ValetOrderMessage对象
        Long tenantId = TenantContextHolder.getTenantId();
        ValetOrderMessage valetOrderMessage = buildValetOrderMessage(receivePhone, messageTemplateCode, tenantId, resendMessageDTO.getServiceName(), orderInfoDO, resendMessageDTO.getCarVin());

        //针对代客下单的子单，传递参数，便于notification服务 生成短链发送短信
        sendValetOrderMessage(Collections.singletonList(valetOrderMessage));

        return new ResendMessageRespVO().setSuccess(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderBindRespVO bindOrder(String orderNumber, String wxPhone, String jlrId, String clientId) {
        log.info("service层 回绑订单 入参，orderNumber：{}，jlrId：{}，clientId：{}", orderNumber, jlrId, clientId);
        Long tenantId = TenantContextHolder.getTenantId();

        // 1. 前置校验（订单是否存在；订单状态是否是已下单；订单来源是否是代客下单；订单是聚合父订单）
        OrderInfoDO orderInfoDO = validateAndRetrieveOrder(orderNumber);
        log.info("订单号：{}，orderInfoDO查询结果：{}", orderNumber, orderInfoDO);

        // 2. 校验t_customer_service_order中的信息 eg（订单是否存在；订单是否已绑定 bind_customer为0）
        CustomerServiceOrderDO customerServiceOrderDO = validateAndRetrieveCustomerServiceOrder(orderNumber);
        log.info("订单号：{}，customerServiceOrderDO查询结果：{}", orderNumber, customerServiceOrderDO);

        // 3. 更新 t_order_info、t_order_terms、t_vcs_order_info 表中的 consumer_code 字段为 consumerCode
        orderInfoDO.setConsumerCode(jlrId);
        // 设置 phone
        orderInfoDO.setWxPhoneMix((String) PhoneUtil.hideBetween(wxPhone));
        orderInfoDO.setWxPhoneMd5(SecureUtil.md5(wxPhone));
        orderInfoDO.setContactPhoneMix((String) PhoneUtil.hideBetween(wxPhone));
        orderInfoDO.setContactPhoneMd5(SecureUtil.md5(wxPhone));
        // 加密手机号
        String encryptWxPhone = null;
        String encryptPhone = null;
        CommonResult<String> commonResult1 = null;
        CommonResult<String> commonResult2 = null;

        try {
            commonResult1 = permissionApi.getEncryptText(wxPhone);
            commonResult2 = permissionApi.getEncryptText(wxPhone);
        } catch (Exception e) {
            log.error("permissionApi解密失败", e);
        }

        if (commonResult1 != null && commonResult1.getData() != null) {
            encryptWxPhone = commonResult1.getData();
            orderInfoDO.setWxPhone(encryptWxPhone);
        }
        if (commonResult2 != null && commonResult2.getData() != null) {
            encryptPhone = commonResult2.getData();
            orderInfoDO.setContactPhone(encryptPhone);
        }

        List<OrderTermsDO> orderTermsDOS = orderTermsDOMapper.selectList(new LambdaQueryWrapperX<OrderTermsDO>().eq(OrderTermsDO::getOrderCode, orderNumber).eq(OrderTermsDO::getIsDeleted, false));
        orderTermsDOS.forEach(orderTermsDO -> orderTermsDO.setConsumerCode(jlrId));
        List<VCSOrderInfoDO> vcsOrderInfoDOS = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>().eq(VCSOrderInfoDO::getOrderCode, orderNumber).eq(VCSOrderInfoDO::getIsDeleted, false));
        vcsOrderInfoDOS.forEach(vcsOrderInfoDO -> vcsOrderInfoDO.setConsumerCode(jlrId));
        log.info("更新t_order_info：{}、t_order_terms:{}、t_vcs_order_info表结果：{}", JSON.toJSONString(orderInfoDO), JSON.toJSONString(vcsOrderInfoDOS), JSON.toJSONString(orderTermsDOS));

        // 4. 更新 t_customer_service_order 表中的 bind_customer 字段
        customerServiceOrderDO.setBindCustomer(BindCustomerEnum.YES.getCode());
        log.info("更新t_customer_service_order表结果：{}", JSON.toJSONString(customerServiceOrderDO));

        // 5. 更新库
        orderInfoDOMapper.updateById(orderInfoDO);
        orderTermsDOMapper.updateBatch(orderTermsDOS);
        vcsOrderInfoDOMapper.updateBatch(vcsOrderInfoDOS);
        customerServiceOrderDOMapper.updateById(customerServiceOrderDO);

        // 6. 异步更新相关的统计表 t_order_statistic、t_vcs_order_statistic,
        List<VCSOrderInfoDO> vcsStatisticList = getVcsOrderInfoDOS(vcsOrderInfoDOS, jlrId);
        log.info("代客下单-回绑订单: 待更新的vcs订单统计数, vcsStatisticList={}", vcsStatisticList);
        // 异步插入统计表
        insertStatisticAsync(jlrId, vcsStatisticList, tenantId, clientId);

        return new OrderBindRespVO().setSuccess(true);
    }

    @Override
    public OrderCreateRespVO preValidateOrderInfo(OrderCreateDTO orderCreateDTO) {
        log.info("service层代客下单选购商品前置校验接口 入参，orderCreateDTO：{}", JSON.toJSONString(orderCreateDTO));

        // - 抽取商品、金额相关参数，在product服务进行校验，返回 skuCode和对应的信息包括一个商品对应的子(组合商品）的信息
        //a.从前端传入的参数中提取校验所需的信息
        OrderValidationInfo orderValidationInfo = extractOrderValidationInfo(orderCreateDTO);
        log.info("代客下单-选购商品时 前置校验：preValidateOrderInfo.extractOrderValidationInfo,orderValidationInfo:{}", JSON.toJSONString(orderValidationInfo));
        //b.调用远程服务
        CommonResult commonResult = productSkuApi.verifyOrderProducts(orderValidationInfo);
        if (commonResult.getCode() != 0) {
            throw exception(new ErrorCode(commonResult.getCode(), commonResult.getMsg()));
        }
        //c.取出最新版本的商品信息 key为sku
        Map<String, ProductSnapshotDTO> latestSnapshotMap = new HashMap<>();
        Map<String, LinkedHashMap> rawDataMap = (Map<String, LinkedHashMap>) commonResult.getData();
        for (Map.Entry<String, LinkedHashMap> entry : rawDataMap.entrySet()) {
            ProductSnapshotDTO snapshot = objectMapper.convertValue(entry.getValue(), ProductSnapshotDTO.class);
            latestSnapshotMap.put(entry.getKey(), snapshot);
        }
        log.info("代客下单-选购商品时 前置校验：最新版本的商品map，latestSnapshotMap:{}", JSON.toJSONString(latestSnapshotMap));

        // carVin::serviceType作为唯一key
        Set<String> carVinAndServiceTypeSet = new HashSet<>();
        Set<String> carVinMd5AndServiceTypeSet = new HashSet<>();
        // 遍历 shopCarItemList 并更新其属性
        buildShopCarItemList(orderCreateDTO, latestSnapshotMap, carVinAndServiceTypeSet, carVinMd5AndServiceTypeSet);
        log.info("代客下单-选购商品时 前置校验：carVinAndServiceTypeSet,carVinMd5AndServiceTypeSet:{}", JSON.toJSONString(carVinAndServiceTypeSet));

        if (CollUtil.isNotEmpty(carVinAndServiceTypeSet)) {
            // - 检查是否存在未支付或售后处理中的商品订单
            OrderCreateRespVO checkResult = checkUnpaidOrAfterSalesVcsOrders(orderCreateDTO, carVinMd5AndServiceTypeSet);
            if (checkResult != null) {
                return checkResult;
            }

            // - 同一辆vin不能重复下单的情况  2.正在激活中 激活关闭中的服务不可购买
            OrderCreateRespVO constraintCheckResponse = checkForExistingVcsOrderConstraints(carVinAndServiceTypeSet);
            if (constraintCheckResponse != null) {
                return constraintCheckResponse;
            }

            // sprint47: 校验是否存在在途的手动续费订单
            return checkManualRenewInTransit(carVinAndServiceTypeSet);
        }

        return null;
    }

    @Override
    public boolean checkOrderInTransit(String carVin, Integer serviceType) {
        log.info("checkOrderInTransit校验是否存在在途订单, carVin:{}, serviceType:{}", carVin, serviceType);
        List<OrderInfoDO> orderInfoList = orderInfoDOMapper.selectOrderInTransit(SecureUtil.md5(carVin), serviceType,
                OrderStatusEnum.getNotInTransitList());
        if (CollUtil.isNotEmpty(orderInfoList)) {
            log.info("carVin:{}, serviceType:{}存在在途订单, {}", carVin, serviceType, orderInfoList);
        }
        return CollUtil.isNotEmpty(orderInfoList);
    }

    @Override
    public List<String> checkOrderInTransitByVinList(List<String> carVinList, Integer serviceType) {
        log.info("checkOrderInTransitByVinList校验是否存在在途订单, carVinList:{}, serviceType:{}", carVinList, serviceType);
        // 遍历carVinList, 对每个carVin进行MD5加密, 并输出Map<carVinMd5, carVin>
        Map<String, String> carVinMap = carVinList.stream().collect(Collectors.toMap(SecureUtil::md5, Function.identity()));
        List<String> carVinMd5List = orderInfoDOMapper.selectOrderInTransitByVinSet(carVinMap.keySet(), serviceType, OrderStatusEnum.getNotInTransitList());
        // 遍历carVinMap, 如果key存在于carVinMd5List中，则将value添加到结果中
        List<String> inTransitVinList = carVinMap.keySet().stream()
                .filter(carVinMd5List::contains)
                .map(carVinMap::get)
                .collect(Collectors.toList());
        log.info("carVinList:{}, serviceType:{}存在在途订单", inTransitVinList, serviceType);
        return inTransitVinList;
    }

    private CustomerServiceOrderDO validateAndRetrieveCustomerServiceOrder(String orderNumber) {
        CustomerServiceOrderDO customerServiceOrderDO = customerServiceOrderDOMapper.selectOne(new LambdaQueryWrapperX<CustomerServiceOrderDO>()
                .eq(CustomerServiceOrderDO::getOrderCode, orderNumber)
                .eq(CustomerServiceOrderDO::getIsDeleted, false)
        );
        if(Objects.isNull(customerServiceOrderDO)){
            log.info("订单号{}在t_customer_service_order表中 没有代客下单信息", orderNumber);
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS_IN_CUSTOMER_SERVICE_RELATION);
        }
        if(customerServiceOrderDO.getBindCustomer().equals(BindCustomerEnum.YES.getCode())){
            log.info("订单号{}在t_customer_service_order表中 已经绑定", orderNumber);
            throw exception(ErrorCodeConstants.ORDER_IS_BIND);
        }

        return customerServiceOrderDO;
    }

    /**
     * 前置校验（订单是否存在；订单状态是否是已下单；订单来源是否是代客下单；订单是聚合父订单）
     * @param orderNumber
     */
    private OrderInfoDO validateAndRetrieveOrder(String orderNumber) {
        // 订单不存在
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderNumber)
                .eq(OrderInfoDO::getIsDeleted, false)
        );
        if (Objects.isNull(orderInfoDO)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }

        // 订单状态非已下单，无法进行回绑操作
        if (!OrderStatusEnum.ORDERED.getCode().equals(orderInfoDO.getOrderStatus())) {
            throw exception(ORDER_STATUS_NOT_PLACED_BIND);
        }

        // 订单为聚合父订单，无法进行回绑操作
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType())) {
            throw exception(ORDER_IS_AGGREGATE_BIND);
        }

        // 订单来源非代客下单，无法进行回绑操作
        if (!OrderChannelCodeEnum.CUSTOMER_SERVICE.getOrderChannelCode().equals(orderInfoDO.getOrderChannel())) {
            throw exception(ORDER_SOURCE_NOT_GUEST_BIND);
        }

        return orderInfoDO;
    }

    private ValetOrderMessage buildValetOrderMessage(String receivePhone, String messageTemplateCode, Long tenantId, String serviceName, OrderInfoDO childOrderInfo, String carVin) {
        // 通过 订单号的前两位获取品牌编码 LR or JA
        String brandCode = StringUtils.left(childOrderInfo.getOrderCode(), 2);
        String orderCode = childOrderInfo.getOrderCode();

        ValetOrderMessage valetOrderMessage = new ValetOrderMessage();
        // 一些全局信息
        valetOrderMessage.setOrderChannel(CUSTOMER_SERVICE.getOrderChannelCode());
        valetOrderMessage.setShortLink(SHORT_LINK_IDENTIFIER);
        valetOrderMessage.setMessageId(ecpIdUtil.nextIdStr());
        valetOrderMessage.setPhoneNumber(receivePhone);
        valetOrderMessage.setTemplateCode(messageTemplateCode);
        valetOrderMessage.setTenantId(tenantId);
        // 独特处理的信息：商品服务名称；品牌；订单号；carVin；url
        valetOrderMessage.setServiceName(serviceName);
        valetOrderMessage.setBrandCode(BrandCodeEnum.getBrandIdByBrandCode(brandCode));
        valetOrderMessage.setOrderNumber(orderCode);
        valetOrderMessage.setCarVin(carVin);
        if (brandCode.equals(BrandCodeEnum.LAND_ROVER.getBrandCode())) {
            valetOrderMessage.setUrl(BEHALFOF_ORDER.getPath());
        } else {
            valetOrderMessage.setUrl(JAGUAR_BEHALFOF_ORDER.getPath());
        }

        return valetOrderMessage;
    }

    /**
     * 返回最近【today-days-1，today-1】支付超时的订单数据
     *
     * @param days 天数
     * @return List<OrderInfoDO>
     */
    private List<OrderInfoDO> getTimeoutOrder(Integer days) {
        LambdaQueryWrapper<OrderInfoDO> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusDays(days + 1L).toLocalDate().atStartOfDay().plusNanos(1);
        LocalDateTime endTime = now.minusMinutes(35);
        wrapper.ge(OrderInfoDO::getOrderTime, startTime)
                .le(OrderInfoDO::getOrderTime, endTime)
                .eq(OrderInfoDO::getOrderStatus, OrderStatusEnum.ORDERED.getCode())
                .eq(OrderInfoDO::getIsDeleted, false);
        return orderInfoDOMapper.selectList(wrapper);
    }

    private void processOrderAfterPaymentSuccess(OrderInfoDO orderInfoDO, List<OrderInfoDO> updateOrderInfos, List<OrderStatusLogDO> orderStatusLogs, String payFinishTime) {
        // 支付成功回调流程
        Integer beforeStatus = orderInfoDO.getOrderStatus();
        Integer event = OrderEventEnum.EVENT_PAYMENT_SUCCESS_CALLBACK.getCode();

        //状态机去变更状态处理逻辑
        orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, null);
        if (StrUtil.isBlank(payFinishTime)) {
            orderInfoDO.setPaymentTime(LocalDateTime.now());
        } else {
            LocalDateTime paymentTime = DateUtil.parseLocalDateTime(payFinishTime, DatePattern.NORM_DATETIME_PATTERN);
            orderInfoDO.setPaymentTime(paymentTime);
        }
        orderInfoDO.setUpdatedTime(LocalDateTime.now());

        //只更新子订单状态日志
        OrderStatusLogDO orderStatusLogCallback = null;
        if (orderInfoDO.getOrderType() != 0) {
            orderStatusLogCallback = assembleOrderStatusLogDO(orderInfoDO.getOrderCode(),
                    beforeStatus, orderInfoDO.getOrderStatus());
            orderStatusLogs.add(orderStatusLogCallback);
        }

        // 添加到待更新列表
        updateOrderInfos.add(orderInfoDO);
    }

    private void updatePaymentRecords(List<OrderInfoDO> orderInfoDOList) {
        for(OrderInfoDO orderInfoDO : orderInfoDOList){
            OrderPaymentRecordsDO orderPaymentRecordsDO = orderPaymentRecordsMapper.selectOne(new LambdaQueryWrapperX<OrderPaymentRecordsDO>()
                    .eq(OrderPaymentRecordsDO::getOrderCode, orderInfoDO.getOrderCode())
                    .eq(BaseDO::getIsDeleted, false)
                    .orderByDesc(OrderPaymentRecordsDO::getId)
                    .last(LIMIT_ONE));
            if (orderPaymentRecordsDO != null) {
                orderPaymentRecordsDO.setPayStatus(PaymentStatus.PAID.getCode());
                orderPaymentRecordsDO.setPayFinishTime(orderInfoDO.getPaymentTime());
                orderPaymentRecordsDO.setUpdatedTime(LocalDateTime.now());
                orderPaymentRecordsMapper.updateById(orderPaymentRecordsDO);
            }
        }
    }

    private void sendKafka(OrderInfoDO orderInfoDO) {
        // 查询订单item列表
        List<OrderItemDO> itemDOList = findItemByOrderCode(orderInfoDO.getOrderCode());

        List<VCSOrderInfoDO> vcsOrderInfoList = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .in(VCSOrderInfoDO::getOrderCode, orderInfoDO.getOrderCode()));

        // 取vin
        String carVin;
        if (!CollectionUtils.isEmpty(vcsOrderInfoList)) {
            carVin = piplDataUtil.getDecodeText(vcsOrderInfoList.get(0).getCarVin());
        } else {
            carVin = null;
        }
        log.info("processOrderAfterPaymentSuccess,解密后的 carVin:{}", carVin);

        log.info("进入支付sms消息发送");
        try {
            if (!CollectionUtils.isEmpty(itemDOList)) {
                // 如果是组合商品，只发送组合商品的短信
                itemDOList.stream().filter(orderItemDO -> OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode().equals(orderItemDO.getOrderItemSpuType()))
                        .findFirst().ifPresentOrElse(bundleItem -> sendPaymentKafka(orderInfoDO, bundleItem, carVin), () -> {
                            for (OrderItemDO orderItemDO : itemDOList) {
                                sendPaymentKafka(orderInfoDO, orderItemDO, carVin);
                            }
                        });
            }
        } catch (Exception e) {
            log.info("send kafka msg error:{}", e.getMessage());
        }
        log.info("进入支付sms消息发送结束");
        //send sub service msg

        log.info("进入支付服务包的消息发送 ");
        try {
            if (!CollectionUtils.isEmpty(vcsOrderInfoList)) {
                for (VCSOrderInfoDO vcsOrderInfoDO : vcsOrderInfoList) {
                    sendFulfilmentKafka(vcsOrderInfoDO);
                }
            }
        } catch (Exception e) {
            log.info("send kafka msg error", e);
        }
        log.info("进入支付服务包的消息发送结束");
    }

    private void sendFulfilmentKafka(VCSOrderInfoDO vcsOrderInfoDO) {
        log.info("进入send fulfilment Kafka 发送方法:{}", JSON.toJSONString(vcsOrderInfoDO));

        FulfilmentMessage fulfilmentMessage = new FulfilmentMessage();
        fulfilmentMessage.setMessageId(ecpIdUtil.nextIdStr());
        fulfilmentMessage.setOrderCode(vcsOrderInfoDO.getOrderCode());
        fulfilmentMessage.setVcsOrderCode(vcsOrderInfoDO.getVcsOrderCode());
        fulfilmentMessage.setOrderItemCode(vcsOrderInfoDO.getOrderItemCode());
        fulfilmentMessage.setServiceType(vcsOrderInfoDO.getServiceType());

        SubscriptionServiceQueryDTO serviceQueryDTO = new SubscriptionServiceQueryDTO();
        // 解密后设置入参
        serviceQueryDTO.setCarVin(piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin()));
        serviceQueryDTO.setIncontrolId(piplDataUtil.getDecodeText(vcsOrderInfoDO.getIncontrolId()));

        LocalDateTime expiryDate = LocalDateTime.now();
        serviceQueryDTO.setServiceType(vcsOrderInfoDO.getServiceType());
        try {
            CommonResult<SubscriptionServiceQueryVO> serviceExpiryDate = subscriptionServiceApi.findServiceExpiryDate(serviceQueryDTO);
            log.info("sendFulfilmentKafka CommonResult<SubscriptionServiceQueryVO> :{}", JSON.toJSONString(serviceExpiryDate));
            SubscriptionServiceQueryVO data = serviceExpiryDate.getData();
            if (data.getExpiryDate() != null) {
                expiryDate = data.getExpiryDate();
                log.info("sendFulfilmentKafka expiryDate :{}", JSON.toJSONString(expiryDate));
            }
        } catch (Exception e) {
            log.error("subscriptionServiceApi.findServiceExpiryDate error:{}", e.getMessage());
        }

        //limit 1 做脏数据容错处理
        OrderItemDO orderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderItemDO::getOrderItemCode, vcsOrderInfoDO.getOrderItemCode())
                .orderByDesc(OrderItemDO::getId).last(Constants.LIMIT_ONE));

        if (orderItemDO != null) {
            String time = AttributeUtil.formatProductAttributes(orderItemDO.getProductAttribute());
            log.info("sendFulfilmentKafka orderItemDO:{}", JSON.toJSONString(orderItemDO));
            long year;
            switch (time) {
                case "一年":
                    year = 1;
                    break;
                case "二年":
                    year = 2;
                    break;
                case "三年":
                    year = 3;
                    break;
                case "四年":
                    year = 4;
                    break;
                case "五年":
                    year = 5;
                    break;
                case "六年":
                    year = 6;
                    break;
                case "十年":
                    year = 10;
                    break;
                default:
                    year = 0;
            }
            if (year == 0) {
                log.error("商品属性值履约年份未匹配到 time:{}",time);
                throw exception(ErrorCodeConstants.PRODUCT_FULFILMENT_ERROR);
            }
            LocalDateTime now = LocalDateTime.now();
            if (expiryDate.isBefore(now)) {
                expiryDate = now;
            }
            LocalDateTime endTime = expiryDate.plusYears(year);
            if (OrderTypeEnum.PIVI.getCode().equals(vcsOrderInfoDO.getServiceType())) {
                endTime = endTime.with(LocalTime.of(23, 59, 59).withNano(0));
            }
            vcsOrderInfoDO.setServiceBeginDate(expiryDate);
            vcsOrderInfoDO.setServiceEndDate(endTime);
            vcsOrderInfoDOMapper.updateById(vcsOrderInfoDO);

            // VIN号解密
            fulfilmentMessage.setVin(piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin()));
            fulfilmentMessage.setServiceBeginDate(expiryDate);
            fulfilmentMessage.setServiceEndDate(endTime);
            fulfilmentMessage.setTenantId(TenantContextHolder.getTenantId());
            fulfilmentMessage.setCreateBy(WebFrameworkUtils.getLoginUserName());
            producerTool.sendMsg(KafkaConstants.ORDER_FUFILMENT_TOPIC, "", JSON.toJSONString(fulfilmentMessage));
            log.info("send payment vcs service msg:{}", JSON.toJSONString(fulfilmentMessage));
        }
    }

    private void sendPaymentKafka(OrderInfoDO orderInfoDO, OrderItemDO orderItemDO, String carVin) {
        OrderPaymentSuccessMessage orderPaymentSuccessMessage = new OrderPaymentSuccessMessage();

        orderPaymentSuccessMessage.setCarVin(carVin);
        orderPaymentSuccessMessage.setMessageId(ecpIdUtil.nextIdStr());
        // 解密手机号
        String phone = phoneNumberDecodeUtil.getDecodePhone(orderInfoDO.getContactPhone());
        orderPaymentSuccessMessage.setPhoneNumber(phone);
        orderPaymentSuccessMessage.setTaskCode(payment);
        orderPaymentSuccessMessage.setTenantId(TenantContextHolder.getTenantId());
        orderPaymentSuccessMessage.setServiceName(orderItemDO.getProductName());
        orderPaymentSuccessMessage.setOrderNumber(orderInfoDO.getOrderCode());

        // 通过 订单号的前两位获取品牌编码 LR or JA
        String brandCode = StringUtils.left(orderInfoDO.getOrderCode(), 2);
        orderPaymentSuccessMessage.setBrandCode(BrandCodeEnum.getBrandIdByBrandCode(brandCode));

        // 生成短链
        // 品牌编码为LR 的短链接生成逻辑
        CommonResult<String> stringCommonResult = null;
        if (brandCode.equals(BrandCodeEnum.LAND_ROVER.getBrandCode())) {
            String path = ShortLinkPathEnum.ORDER_DETAIL.getPath() + orderInfoDO.getOrderCode() + Constants.SHORT_LINK_QUERY;
            ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
            shortLinkReqDto.setPath(path);
            try {
                stringCommonResult = shorLinkAPI.genShortLink(shortLinkReqDto);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (stringCommonResult != null && stringCommonResult.getData() != null) {
                orderPaymentSuccessMessage.setWxUrl(stringCommonResult.getData());
            }
        } else {
            String path = ShortLinkPathEnum.JAGUAR_ORDER_DETAIL.getPath();
            String query = "orderCode=" + orderInfoDO.getOrderCode() + Constants.SHORT_LINK_QUERY;
            log.info("generate JaguarLink path:{} query:{}", path, query);
            ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
            shortLinkReqDto.setPath(path);
            shortLinkReqDto.setQuery(query);
            try {
                stringCommonResult = shorLinkAPI.genJaguarLink(shortLinkReqDto);
            } catch (Exception e) {
                log.error("generate JaguarLink error:{}", e.getMessage());
                e.printStackTrace();
            }
            if (stringCommonResult != null && stringCommonResult.getData() != null) {
                orderPaymentSuccessMessage.setWxUrl(stringCommonResult.getData());
            }
        }

        producerTool.sendMsg(KafkaConstants.ORDER_SUCCESSFUL_TOPIC, "", JSON.toJSONString(orderPaymentSuccessMessage));

        log.info("send PaymentKafka success msg:{}", JSON.toJSONString(orderPaymentSuccessMessage));
    }

    private List<OrderItemDO> findItemByOrderCode(String orderCode) {
        return orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderItemDO::getOrderCode, orderCode));
    }

    private void processOrderAfterPayment(OrderInfoDO orderInfoDO, List<OrderInfoDO> updateOrderInfos, List<OrderStatusLogDO> orderStatusLogs) {
        //发起支付流程
        Integer beforeStatus = orderInfoDO.getOrderStatus();
        Integer event = OrderEventEnum.EVENT_PAYMENT_SUCCESS_CALLBACK.getCode();

        //状态机去变更状态处理逻辑
        orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, null);

        //只记录子订单状态变化
        OrderStatusLogDO orderStatusLog = null;
        if (!orderInfoDO.getParentOrderCode().equals(orderInfoDO.getOrderCode())) {
            orderStatusLog = assembleOrderStatusLogDO(orderInfoDO.getOrderCode(),
                    beforeStatus, orderInfoDO.getOrderStatus());
        }

        // 添加到待更新列表
        updateOrderInfos.add(orderInfoDO);
        if (orderStatusLog != null) {
            orderStatusLogs.add(orderStatusLog);
        }
    }

    private OrderStatusLogDO assembleOrderStatusLogDO(String code, Integer beforeStatus, Integer afterStatus) {
        OrderStatusLogDO orderStatusLog = new OrderStatusLogDO();
        orderStatusLog.setOrderCode(code);
        orderStatusLog.setBeforeStatus(beforeStatus);
        orderStatusLog.setAfterStatus(afterStatus);
        orderStatusLog.setChangeTime(LocalDateTime.now());
        return orderStatusLog;
    }

    /**
     * 使用orderCode获取订单下单时间
     *
     * @param orderCode 订单编号
     * @return CommonResult<OrderInvoiceDTO>
     */
    @Override
    public CommonResult<OrderInvoiceDTO> queryOrderInfoByOrderCode(String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            return null;
        }
        LambdaQueryWrapper<OrderInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, false);
        List<OrderInfoDO> orderInfoDOList = orderInfoDOMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(orderInfoDOList)) {
            return CommonResult.error(ErrorCodeConstants.INVALID_ORDER);
        }
        if (orderInfoDOList.size() > 1) {
            log.warn("使用orderCode查询orderInfo数据大于1, orderCode:{}, orderInfoDOList:{}", orderCode, orderInfoDOList);
        }
        OrderInfoDO orderInfoDO = orderInfoDOList.get(0);
        OrderInvoiceDTO orderInvoiceDTO = new OrderInvoiceDTO();
        BeanUtils.copyProperties(orderInfoDO, orderInvoiceDTO);

        //查询支付单号
        OrderPaymentRecordsDO orderPaymentRecordsDO = orderPaymentRecordsMapper.queryByOrderCode(orderCode, orderInfoDO.getParentOrderCode());
        if (orderPaymentRecordsDO != null) {
            orderInvoiceDTO.setPayApplyNo(orderPaymentRecordsDO.getPayApplyNo());
        }
        return CommonResult.success(orderInvoiceDTO);
    }

    @Override
    public Set<String> getAfterSalesOrderCode(List<String> orderCodeList) {
        if (CollUtil.isEmpty(orderCodeList)) {
            return null;
        }
        LambdaQueryWrapper<OrderInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderInfoDO::getOrderCode, orderCodeList)
                .eq(OrderInfoDO::getOrderStatus, OrderStatusEnum.AFTER_SALES.getCode())
                .eq(OrderInfoDO::getIsDeleted, false);
        List<OrderInfoDO> orderInfoDOList = orderInfoDOMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(orderInfoDOList)) {
            return null;
        }
        log.info("queryOrderStatusMap, orderCodeList:{}, orderInfoDOList:{}", orderCodeList, orderInfoDOList);
        return orderInfoDOList.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toSet());
    }

    /**
     * 设置商品的服务启停时间和服务状态。
     * 此方法调用 vcsOrderFulfilmentApi 来获取服务的当前状态和时间信息，并更新传入的商品信息对象。
     *
     * @param orderItemCode  订单项编码，用于查询服务状态。
     * @param itemInfoVO     需要更新服务信息的商品信息视图对象。
     * @param vcsOrderInfoDO
     */
    private void setServiceTimeAndStatusForItem(String orderItemCode, ProductItemInfoVO itemInfoVO, VCSOrderInfoDO vcsOrderInfoDO) {
        // 调用 API 获取服务状态和时间信息
        CommonResult<VcsOrderFulfilmentRespVO> view = null;
        try {
            view = vcsOrderFulfilmentApi.view(orderItemCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        itemInfoVO.setServiceBeginDate(vcsOrderInfoDO.getServiceBeginDate());
        itemInfoVO.setServiceEndDate(vcsOrderInfoDO.getServiceEndDate());
        itemInfoVO.setActualEndDate(vcsOrderInfoDO.getServiceEndDate());
        // 检查 API 响应是否有效
        if (view == null || view.getData() == null) {
            // 如果响应无效，设置默认状态为"-"
            itemInfoVO.setServiceStatusDesc(" - ");
        } else {
            // 如果响应有效，设置服务时间和状态
            itemInfoVO.setServiceStatus(view.getData().getServiceStatus());
            itemInfoVO.setServiceStatusDesc(view.getData().getServiceStatusDesc());
        }
    }

    /**
     * 组装小程序端 商品信息
     * t_order_item表能查到的信息
     *
     * @param orderItemDO
     * @return
     */
    private ProductBrandCategoriedItemInfoVO assembleProductBrandCategoriedItemInfo(OrderItemDO orderItemDO) {
        ProductBrandCategoriedItemInfoVO productBrandCategoriedItemInfoVO = new ProductBrandCategoriedItemInfoVO();

        productBrandCategoriedItemInfoVO.setOrderItemCode(orderItemDO.getOrderItemCode());
        productBrandCategoriedItemInfoVO.setCostAmount(MoneyUtil.convertFromCents(new BigDecimal(orderItemDO.getCostAmount()))) ;
        productBrandCategoriedItemInfoVO.setProductVersionCode(orderItemDO.getProductVersionCode());
        productBrandCategoriedItemInfoVO.setProductCode(orderItemDO.getProductCode());
        productBrandCategoriedItemInfoVO.setProductSkuCode(orderItemDO.getProductSkuCode());

        productBrandCategoriedItemInfoVO.setProductName(orderItemDO.getProductName());
        productBrandCategoriedItemInfoVO.setProductImageUrl(orderItemDO.getProductImageUrl());
        productBrandCategoriedItemInfoVO.setProductAttribute(AttributeUtil.formatProductAttributes(orderItemDO.getProductAttribute()));
        if (orderItemDO.getProductMarketPrice() != null) {
            productBrandCategoriedItemInfoVO.setProductMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(orderItemDO.getProductMarketPrice())));
        }
        productBrandCategoriedItemInfoVO.setProductSalePrice(MoneyUtil.convertFromCents(new BigDecimal(orderItemDO.getProductSalePrice())));
        productBrandCategoriedItemInfoVO.setProductQuantity(orderItemDO.getProductQuantity());
        productBrandCategoriedItemInfoVO.setOrderItemSpuType(orderItemDO.getOrderItemSpuType());
        productBrandCategoriedItemInfoVO.setRemark(orderItemDO.getRemark());

        return productBrandCategoriedItemInfoVO;
    }

    private void assembleLreProductInfoVO(OrderInfoDO orderInfoDO, OrderDetailRespVO orderDetailRespVO) {
        // 根据订单号查询所有相关的商品项
        List<OrderItemDO> orderItems = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
            .eq(OrderItemDO::getOrderCode, orderInfoDO.getOrderCode())
            .eq(OrderItemDO::getIsDeleted, 0));


        // 检查是否查询到商品项
        if (orderItems == null || orderItems.isEmpty()) {
            return;
        }

        // 商品item信息
        List<ECouponOrderItemVO> itemInfoVOList = orderItems.stream()
            .map(item -> convertToECouponOrderItemVO(item))
            .collect(Collectors.toList());

        OrderDetailProductInfoVO productInfoVO = new OrderDetailProductInfoVO();
        productInfoVO.setECouponProductItemInfoList(itemInfoVOList);
        orderDetailRespVO.setProductInfo(productInfoVO);
    }
    
    private ECouponOrderItemVO convertToECouponOrderItemVO(OrderItemDO item) {
        ECouponOrderItemVO itemInfoVO = new ECouponOrderItemVO();
        
        // 设置基本信息
        setECouponBasicInfo(item, itemInfoVO);
        
        // 设置优惠券详情信息
        List<OrderCouponDetailDO> orderCouponDetailDOList = getCouponDetails(item.getOrderItemCode());
        setItenInfoVo(item, orderCouponDetailDOList, itemInfoVO);
        
        // 设置已核销数量
        setUsedCouponCount(orderCouponDetailDOList, itemInfoVO);
        
        // 设置售后状态信息
        setECouponAfterSalesInfo(item.getOrderItemCode(), itemInfoVO);
        
        // 设置价格信息
        setECouponPriceInfo(item, itemInfoVO);
        
        // 设置折扣信息
        setECouponDiscountInfo(item.getOrderItemCode(), itemInfoVO);
        
        return itemInfoVO;
    }
    
    private List<OrderCouponDetailDO> getCouponDetails(String orderItemCode) {
        return orderCouponDetailDOMapper.selectList(new LambdaQueryWrapper<OrderCouponDetailDO>()
            .eq(OrderCouponDetailDO::getOrderItemCode, orderItemCode)
            .eq(OrderCouponDetailDO::getIsDeleted, false));
    }
    
    private void setECouponBasicInfo(OrderItemDO item, ECouponOrderItemVO itemInfoVO) {
        itemInfoVO.setOrderItemCode(item.getOrderItemCode());
        itemInfoVO.setCouponModelCode(item.getCouponModelCode());
        itemInfoVO.setProductName(item.getProductName());
        itemInfoVO.setProductAttribute(AttributeUtil.formatProductAttributes(item.getProductAttribute()));
        itemInfoVO.setProductQuantity(item.getProductQuantity());
        itemInfoVO.setItemStatus(item.getItemStatus());
    }
    
    private void setUsedCouponCount(List<OrderCouponDetailDO> orderCouponDetailDOList, ECouponOrderItemVO itemInfoVO) {
        itemInfoVO.setUsedCouponCount((int) orderCouponDetailDOList.stream()
            .filter(orderCouponDetailDO -> orderCouponDetailDO.getStatus() == EcouponStatusEnum.VERIFIED.getCode())
            .count());
    }
    
    private void setECouponAfterSalesInfo(String orderItemCode, ECouponOrderItemVO itemInfoVO) {
        OrderRefundItemDO orderRefundItemDO = orderRefundItemDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundItemDO>()
            .eq(OrderRefundItemDO::getOrderItemCode, orderItemCode)
            .eq(OrderRefundItemDO::getIsDeleted, false)
            .orderByAsc(OrderRefundItemDO::getCreatedTime)
            .last(Constants.LIMIT_ONE));

        if (orderRefundItemDO != null) {
            itemInfoVO.setRefundCouponCount(orderRefundItemDO.getRefundQuantity());

            OrderRefundDO orderRefundDO = orderRefundDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundDO>()
                .eq(OrderRefundDO::getRefundOrderCode, orderRefundItemDO.getRefundOrderCode())
                .eq(OrderRefundDO::getIsDeleted, false)
                .orderByDesc(OrderRefundDO::getCreatedTime)
                .last(Constants.LIMIT_ONE));

            if (orderRefundDO != null) {
                itemInfoVO.setAfterSalesStatus(orderRefundDO.getCouponRefundStatus());
            }
        }
    }
    
    private void setECouponPriceInfo(OrderItemDO item, ECouponOrderItemVO itemInfoVO) {
        if (item.getProductMarketPrice() != null) {
            itemInfoVO.setProductMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(item.getProductMarketPrice())));
        }
        
        itemInfoVO.setProductSalePrice(MoneyUtil.convertFromCents(new BigDecimal(item.getProductSalePrice())));
        itemInfoVO.setCostAmount(MoneyUtil.convertFromCents(new BigDecimal(item.getCostAmount())));
    }
    
    private void setECouponDiscountInfo(String orderItemCode, ECouponOrderItemVO itemInfoVO) {
        OrderDiscountDetailDO orderDiscountDetailDO = orderDiscountDetailDOMapper.selectOne(new LambdaQueryWrapper<OrderDiscountDetailDO>()
            .eq(OrderDiscountDetailDO::getOrderItemCode, orderItemCode)
            .eq(OrderDiscountDetailDO::getIsDeleted, false)
            .last(Constants.LIMIT_ONE));

        if (orderDiscountDetailDO == null) {
            return;
        }
        
        if (orderDiscountDetailDO.getDiscountType().equals(DiscountTypeEnum.COUPON_DISCOUNT.getType()) 
                && orderDiscountDetailDO.getCouponCode() != null) {
            setECouponDiscountInfo(orderDiscountDetailDO, itemInfoVO);
        } else if (orderDiscountDetailDO.getDiscountType().equals(DiscountTypeEnum.POINT_DISCOUNT.getType())) {
            itemInfoVO.setPointAmount(orderDiscountDetailDO.getCostPoints());
        }
    }
    
    private void setECouponDiscountInfo(OrderDiscountDetailDO orderDiscountDetailDO, ECouponOrderItemVO itemInfoVO) {
        itemInfoVO.setDiscountCouponType(CouponTypeEnum.getByType(orderDiscountDetailDO.getCouponModelClassify()).getDesc());
        itemInfoVO.setDiscountCouponName(orderDiscountDetailDO.getCouponModelName());
        itemInfoVO.setDiscountCouponCode(orderDiscountDetailDO.getCouponCode());
        itemInfoVO.setDiscountAmount(MoneyUtil.convertFromCents(new BigDecimal(orderDiscountDetailDO.getDiscountAmount())));
    }

    private void assembleBgProductInfoVO(OrderInfoDO orderInfoDO, OrderDetailRespVO orderDetailRespVO) {
        // 根据订单号查询所有相关的商品项
        List<OrderItemDO> orderItems = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
            .eq(OrderItemDO::getOrderCode, orderInfoDO.getOrderCode())
            .eq(OrderItemDO::getIsDeleted, 0)
            .orderByAsc(OrderItemDO::getId));

        // 检查是否查询到商品项
        if (orderItems == null || orderItems.isEmpty()) {
            return;
        }

        // 商品item信息
        List<BrandGoodsOrderItemVO> itemInfoVOList = orderItems.stream()
            .map(item -> convertToBrandGoodsOrderItemVO(item))
            .collect(Collectors.toList());

        OrderDetailProductInfoVO productInfoVO = new OrderDetailProductInfoVO();
        productInfoVO.setBrandGoodsProductItemInfoList(itemInfoVOList);
        orderDetailRespVO.setProductInfo(productInfoVO);
    }
    
    private BrandGoodsOrderItemVO convertToBrandGoodsOrderItemVO(OrderItemDO item) {
        BrandGoodsOrderItemVO itemInfoVO = new BrandGoodsOrderItemVO();
        
        // 设置基本信息
        setBasicItemInfo(item, itemInfoVO);
        
        // 设置物流信息
        setLogisticsInfo(item.getOrderItemCode(), itemInfoVO);
        
        // 设置退款相关信息
        setBgItenInfoVo(item, itemInfoVO);
        
        // 设置售后状态信息
        setAfterSalesInfo(item.getOrderItemCode(), itemInfoVO);
        
        // 设置价格信息
        setPriceInfo(item, itemInfoVO);
        
        // 设置折扣信息
        setDiscountInfo(item.getOrderItemCode(), itemInfoVO);
        
        return itemInfoVO;
    }
    
    private void setBasicItemInfo(OrderItemDO item, BrandGoodsOrderItemVO itemInfoVO) {
        itemInfoVO.setOrderItemCode(item.getOrderItemCode());
        itemInfoVO.setProductName(item.getProductName());
        itemInfoVO.setProductSkuCode(item.getProductSkuCode());
        itemInfoVO.setKingdeeSkuCode(item.getKingdeeSkuCode());
        itemInfoVO.setProductAttribute(AttributeUtil.formatProductAttributes(item.getProductAttribute()));
        itemInfoVO.setProductQuantity(item.getProductQuantity());
        itemInfoVO.setItemStatus(item.getItemStatus());
    }
    
    private void setLogisticsInfo(String orderItemCode, BrandGoodsOrderItemVO itemInfoVO) {
        OrderItemLogisticsDO orderItemLogisticsDO = orderItemLogisticsDOMapper.selectOne(new LambdaQueryWrapper<OrderItemLogisticsDO>()
            .eq(OrderItemLogisticsDO::getOrderItemCode, orderItemCode)
            .eq(OrderItemLogisticsDO::getIsDeleted, false)
            .last(Constants.LIMIT_ONE));

        if (orderItemLogisticsDO != null) {
            itemInfoVO.setLogisticsNo(orderItemLogisticsDO.getLogisticsNo());
            itemInfoVO.setLogisticsStatus(orderItemLogisticsDO.getLogisticsStatus());
            
            if (orderItemLogisticsDO.getNoReasonReturnsTime() != null) {
                itemInfoVO.setNoReasonReturnsTime(orderItemLogisticsDO.getNoReasonReturnsTime().toString());
            }
        }
    }
    
    private void setAfterSalesInfo(String orderItemCode, BrandGoodsOrderItemVO itemInfoVO) {
        OrderRefundItemDO orderRefundItemDO = orderRefundItemDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundItemDO>()
            .eq(OrderRefundItemDO::getOrderItemCode, orderItemCode)
            .eq(OrderRefundItemDO::getIsDeleted, false)
            .orderByDesc(OrderRefundItemDO::getCreatedTime)
            .last(Constants.LIMIT_ONE));

        if (orderRefundItemDO != null) {
            OrderRefundDO orderRefundDO = orderRefundDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundDO>()
                .eq(OrderRefundDO::getRefundOrderCode, orderRefundItemDO.getRefundOrderCode())
                .eq(OrderRefundDO::getIsDeleted, false)
                .orderByDesc(OrderRefundDO::getCreatedTime)
                .last(Constants.LIMIT_ONE));

            if (orderRefundDO != null) {
                itemInfoVO.setAfterSalesStatus(orderRefundDO.getLogisticsRefundStatus());
                itemInfoVO.setRefundOrderStatus(orderRefundDO.getRefundOrderStatus());
            }
        }
    }
    
    private void setPriceInfo(OrderItemDO item, BrandGoodsOrderItemVO itemInfoVO) {
        if (item.getProductMarketPrice() != null) {
            itemInfoVO.setProductMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(item.getProductMarketPrice())));
        }
        
        itemInfoVO.setProductSalePrice(MoneyUtil.convertFromCents(new BigDecimal(item.getProductSalePrice())));
        itemInfoVO.setCostAmount(MoneyUtil.convertFromCents(new BigDecimal(item.getCostAmount())));
    }
    
    private void setDiscountInfo(String orderItemCode, BrandGoodsOrderItemVO itemInfoVO) {
        OrderDiscountDetailDO orderDiscountDetailDO = orderDiscountDetailDOMapper.selectOne(new LambdaQueryWrapper<OrderDiscountDetailDO>()
            .eq(OrderDiscountDetailDO::getOrderItemCode, orderItemCode)
            .eq(OrderDiscountDetailDO::getIsDeleted, false)
            .last(Constants.LIMIT_ONE));

        if (orderDiscountDetailDO == null) {
            return;
        }
        
        if (orderDiscountDetailDO.getDiscountType().equals(DiscountTypeEnum.COUPON_DISCOUNT.getType()) 
                && orderDiscountDetailDO.getCouponCode() != null) {
            setCouponDiscountInfo(orderDiscountDetailDO, itemInfoVO);
        } else if (orderDiscountDetailDO.getDiscountType().equals(DiscountTypeEnum.POINT_DISCOUNT.getType())) {
            itemInfoVO.setPointAmount(orderDiscountDetailDO.getCostPoints());
        }
    }
    
    private void setCouponDiscountInfo(OrderDiscountDetailDO orderDiscountDetailDO, BrandGoodsOrderItemVO itemInfoVO) {
        itemInfoVO.setDiscountCouponType(CouponTypeEnum.getByType(orderDiscountDetailDO.getCouponModelClassify()).getDesc());
        itemInfoVO.setDiscountCouponName(orderDiscountDetailDO.getCouponModelName());
        itemInfoVO.setDiscountCouponCode(orderDiscountDetailDO.getCouponCode());
        itemInfoVO.setDiscountAmount(MoneyUtil.convertFromCents(new BigDecimal(orderDiscountDetailDO.getDiscountAmount())));
    }

    private void setItenInfoVo(OrderItemDO item, List<OrderCouponDetailDO> orderCouponDetailDOList, ECouponOrderItemVO itemInfoVO) {
        if (CollUtil.isNotEmpty(orderCouponDetailDOList)) {
            // 兑换券列表
            itemInfoVO.setCouponCodeList(orderCouponDetailDOList.stream().map(
                orderCouponDetailDO -> {
                    return geteCouponOrderDetailVO(orderCouponDetailDO);
                }
            ).collect(Collectors.toList()));

            // 有效期开始时间
            itemInfoVO.setValidStartTime(String.valueOf(orderCouponDetailDOList.get(0).getValidStartTime() == null ? "" : orderCouponDetailDOList.get(0).getValidStartTime()));

            // 有效期结束时间
            itemInfoVO.setValidEndTime(String.valueOf(orderCouponDetailDOList.get(0).getValidEndTime() == null ? "" : orderCouponDetailDOList.get(0).getValidEndTime()));

            BigDecimal totalCouponCount = BigDecimal.valueOf(orderCouponDetailDOList.stream().count());
            BigDecimal usedCouponCount = BigDecimal.valueOf(orderCouponDetailDOList.stream().filter(orderCouponDetailDO -> orderCouponDetailDO.getStatus() == EcouponStatusEnum.VERIFIED.getCode()).count());

            // Prevent division by zero
            BigDecimal singleCouponMaxRefundableAmount;
            if (totalCouponCount.compareTo(BigDecimal.ZERO) == 0) {
                itemInfoVO.setMaxRefundableAmount(MoneyUtil.convertFromCents(BigDecimal.ZERO));
            } else {

                // 实付单价 = 总金额 / 优惠券数量
                singleCouponMaxRefundableAmount = BigDecimal.valueOf(item.getCostAmount()).divide(totalCouponCount, 2, RoundingMode.HALF_UP);

                // 已核销金额 = 实付单价 * 已核销优惠券数量
                BigDecimal usedCouponAmount = singleCouponMaxRefundableAmount.multiply(usedCouponCount);

                // 初始化退款金额为零
                BigDecimal refundMoney = BigDecimal.ZERO;

                // 查询订单退款项信息（获取最新的一个）
                OrderRefundItemDO orderRefundItemDO = orderRefundItemDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundItemDO>()
                    .eq(OrderRefundItemDO::getOrderItemCode, item.getOrderItemCode())
                    .eq(OrderRefundItemDO::getIsDeleted, false)
                    .orderByDesc(OrderRefundItemDO::getCreatedTime)
                    .last(Constants.LIMIT_ONE));

                // 查询订单退款信息
                OrderRefundDO orderRefundDO = null;
                if (orderRefundItemDO != null) {
                    orderRefundDO = orderRefundDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundDO>()
                        .eq(OrderRefundDO::getRefundOrderCode, orderRefundItemDO.getRefundOrderCode())
                        .eq(OrderRefundDO::getIsDeleted, false));

                    itemInfoVO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
                }

                // 如果退款状态为处理中、分账处理中或已完成，则获取退款金额
                if (orderRefundDO != null && Set.of(
                        RefundCouponStatusEnum.REFUND_PROCESSING.getCode(),
                        RefundCouponStatusEnum.SPLIT_REFUND_PROCESSING.getCode(),
                        RefundCouponStatusEnum.REFUND_COMPLETED.getCode()
                ).contains(orderRefundDO.getCouponRefundStatus())) {
                    refundMoney = BigDecimal.valueOf(orderRefundItemDO.getRefundMoney());
                }

                // 最大可退款金额 = 实付金额 - 已退款金额 - 已核销金额
                itemInfoVO.setMaxRefundableAmount(
                    MoneyUtil.convertFromCents(
                        BigDecimal.valueOf(refundHandler.getMaxRefundMoney(item,RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode()))
                    )
                );
            }
        }
    }

    private void setBgItenInfoVo(OrderItemDO item, BrandGoodsOrderItemVO itemInfoVO) {
        // 添加对item和itemInfoVO的空检查
        if (item == null || itemInfoVO == null) {
            log.warn("OrderItemDO或BrandGoodsOrderItemVO为空，无法设置品牌商品信息");
            return;
        }
        if (item.getProductQuantity() != 0) {
            // 查询订单退款项信息（获取最新的一个）
            OrderRefundItemDO orderRefundItemDO = orderRefundItemDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundItemDO>()
                .eq(OrderRefundItemDO::getOrderItemCode, item.getOrderItemCode())
                .eq(OrderRefundItemDO::getIsDeleted, false)
                .orderByDesc(OrderRefundItemDO::getCreatedTime)
                .last(Constants.LIMIT_ONE));

            // 查询订单退款信息
            OrderRefundDO orderRefundDO = null;
            if (orderRefundItemDO != null) {
                orderRefundDO = orderRefundDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundDO>()
                    .eq(OrderRefundDO::getRefundOrderCode, orderRefundItemDO.getRefundOrderCode())
                    .eq(OrderRefundDO::getIsDeleted, false));

                itemInfoVO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
            }

            // 最大可退款金额 = 实付金额 - 已退款金额 - 已核销金额
            itemInfoVO.setMaxRefundableAmount(
                MoneyUtil.convertFromCents(
                    BigDecimal.valueOf(refundHandler.getLogisticsMaxRefundMoney(item,null))
                )
            );
            
        }
    }

    @NotNull
    private static ECouponOrderDetailVO geteCouponOrderDetailVO(OrderCouponDetailDO orderCouponDetailDO) {
        ECouponOrderDetailVO couponCodeVO = new ECouponOrderDetailVO();
        couponCodeVO.setCouponCode(orderCouponDetailDO.getCouponCode());
        couponCodeVO.setCouponModelCode(orderCouponDetailDO.getCouponModelCode());
        couponCodeVO.setStatus(orderCouponDetailDO.getStatus());
        couponCodeVO.setValidStartTime(String.valueOf(orderCouponDetailDO.getValidStartTime() == null ? "" : orderCouponDetailDO.getValidStartTime()));
        couponCodeVO.setValidEndTime(String.valueOf(orderCouponDetailDO.getValidEndTime() == null ? "" : orderCouponDetailDO.getValidEndTime()));
        couponCodeVO.setUsedTime(String.valueOf(orderCouponDetailDO.getUsedTime() == null ? "" : orderCouponDetailDO.getUsedTime()));
        couponCodeVO.setSendTime(String.valueOf(orderCouponDetailDO.getSendTime() == null ? "" : orderCouponDetailDO.getSendTime()));
        return couponCodeVO;
    }

    /**
     * @param orderInfoDO
     * @return
     */
    private void assembleProductAndVehicleInfoVO(OrderInfoDO orderInfoDO, OrderDetailRespVO orderDetailRespVO) {
        // 根据订单号查询所有相关的商品项
        List<OrderItemDO> orderItems = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderItemDO::getIsDeleted, 0));

        // 检查是否查询到商品项
        if (orderItems == null || orderItems.isEmpty()) {
            return;
        }
        OrderItemDO bundleItem = orderItems.stream().filter(item -> OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode().equals(item.getOrderItemSpuType())).findFirst().orElse(null);
        // 需要查询服务状态的code列表
        List<String> orderItemCodes;
        // 需要查询商品取消状态的code
        String orderItemCode;
        // 商品item信息
        ProductBrandCategoriedItemInfoVO itemInfoVO;
        // 商品项存在组合商品
        if (bundleItem != null) {
            orderItemCode = bundleItem.getOrderItemCode();
            //a.设置捆绑商品基本信息 t_order_item表能查到的信息
            itemInfoVO = assembleProductBrandCategoriedItemInfo(bundleItem);
            // 设置组合商品信息
            List<ProductBrandCategoriedItemInfoVO> childItemList = orderItems.stream().filter(item -> OrderItemSpuTypeEnum.NORMAL_GOOD.getCode().equals(item.getOrderItemSpuType()))
                    .map(this::assembleProductBrandCategoriedItemInfo)
                    .collect(Collectors.toList());
            orderItemCodes = childItemList.stream().map(ProductBrandCategoriedItemInfoVO::getOrderItemCode).collect(Collectors.toList());
            itemInfoVO.setNext(childItemList);
        } else {
            // 不存在默认只有一个商品项
            OrderItemDO orderItemDO = orderItems.get(0);
            orderItemCode = orderItemDO.getOrderItemCode();
            //a.设置商品基本信息 t_order_item表能查到的信息
            itemInfoVO = assembleProductBrandCategoriedItemInfo(orderItemDO);
            orderItemCodes = List.of(orderItemCode);
        }
        //2. 组装车型信息
        buildAdminCarInfoVO(itemInfoVO, orderDetailRespVO, orderItemCodes);
        //3. 组装商品取消状态
        //TODO c.通过item对象的orderCode查询t_order_info中对应的order_status和 通过item对象的orderItemCode查询t_order_refund_item中对应的refund_order_code，关联查询t_order_refund表中refund_order_status
        // 拿到这两个值后，根据这两个值查询t_order_status_mapping中全量 取出对应的order_status、refund_order_status、customer_order_status_view、customer_after_sales_order_status_view、operation_origin_order_status_view、operation_origin_order_cancel_status_view
        // 后续可根据redis缓存优化
        //t_order_refund,refund_order_status 和 t_order_info refund_status不是一个字段
        OrderStatusMappingDO orderStatusMapping = getOrderStatusMapping(orderInfoDO, orderItemCode);
        if (orderStatusMapping != null) {
            itemInfoVO.setOperationOriginOrderCancelStatusView(orderStatusMapping.getOperationOriginOrderCancelStatusView());
        }
        setRefundInfo(itemInfoVO, orderInfoDO);

        OrderDetailProductInfoVO productInfoVO = new OrderDetailProductInfoVO();
        productInfoVO.setProductItemInfoList(List.of(itemInfoVO));
        orderDetailRespVO.setProductInfo(productInfoVO);
    }

    public OrderStatusMappingDO getOrderStatusMapping(OrderInfoDO orderInfoDO, String orderItemCode) {
        Integer orderStatus = orderInfoDO.getOrderStatus();
        if (orderStatus == null) {
            orderStatus = -1;
        }

        Integer refundOrderStatus = orderRefundItemDOMapper.getRefundOrderStatusByOrderItemCode(orderItemCode);
        if (refundOrderStatus == null) {
            refundOrderStatus = -1;
        }

        return orderStatusMappingDOMapper.getStatusMapping(orderStatus, refundOrderStatus);
    }

    /**
     * 根据订单号组装车辆信息视图对象。
     *
     * @param vcsOrderInfo vcs订单
     * @return OrderDetailVehicleInfoVO 车辆信息视图对象
     */
    private OrderDetailVehicleInfoVO assembleVehicleInfoVO(VCSOrderInfoDO vcsOrderInfo) {
        String carVin = vcsOrderInfo.getCarVin();

        // 2. 通过远程调用在consumer服务中查询car_vin的完整数据
        CommonResult<List<IncontrolVehicleByCarDTO>> vehicleResult = new CommonResult<>();
        OrderDetailVehicleInfoVO vehicleInfoVO = new OrderDetailVehicleInfoVO();

        try {
            // 用原文去查询
            carVin = phoneNumberDecodeUtil.getDecodePhone(carVin);
            vehicleResult = incontrolVehicleAPI.getIncontrolVehicleByCarVin(Collections.singletonList(carVin));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (Objects.isNull(vehicleResult.getCode()) || vehicleResult.getCode() != 0 || CollUtil.isEmpty(vehicleResult.getData())) {
            //            throw new RuntimeException("获取车辆信息失败: " + vehicleResult.getMsg());
            return vehicleInfoVO;
        }

        // 取第一个车辆信息（假设每个carVin对应唯一的车辆信息）
        IncontrolVehicleByCarDTO vehicleInfo = vehicleResult.getData().get(0);

        // 3. 组装OrderDetailVehicleInfoVO
        vehicleInfoVO.setCarVin(vcsOrderInfo.getCarVin());
        vehicleInfoVO.setCarVinMix(vcsOrderInfo.getCarVinMix());
        vehicleInfoVO.setBrandCode(vehicleInfo.getBrandCode());
        vehicleInfoVO.setBrandName(vehicleInfo.getBrandName());
        vehicleInfoVO.setSeriesCode(vcsOrderInfo.getSeriesCode());
        vehicleInfoVO.setSeriesName(vcsOrderInfo.getSeriesName());
        vehicleInfoVO.setModelYear(vehicleInfo.getModelYear());
        vehicleInfoVO.setConfigCode(vehicleInfo.getConfigCode());
        vehicleInfoVO.setConfigName(vehicleInfo.getConfigName());

        // 返回组装好的车辆信息视图对象
        return vehicleInfoVO;
    }

    /**
     * 根据订单信息组装客户信息视图对象。
     *
     * @param orderInfoDO 订单信息数据对象
     * @return OrderDetailCustomerInfoVO 客户信息视图对象
     */
    private OrderDetailCustomerInfoVO assembleCustomerInfoVO(OrderInfoDO orderInfoDO) {
        OrderDetailCustomerInfoVO customerInfoVO = new OrderDetailCustomerInfoVO();
        // 客户微信昵称目前没有数据，任何权限展示都是"-"
        // customerInfoVO.setWxNickName(orderInfoDO.getWxNickName());
        customerInfoVO.setWxNickName("-");
        customerInfoVO.setWxPhone(orderInfoDO.getWxPhone());
        customerInfoVO.setWxPhoneMix(orderInfoDO.getWxPhoneMix());
        customerInfoVO.setContactPhone(orderInfoDO.getContactPhone());
        customerInfoVO.setContactPhoneMix(orderInfoDO.getContactPhoneMix());
        if (orderInfoDO.getBusinessCode().equals(BusinessIdEnum.BRAND_GOODS.getCode())){
            // 通过orderCode 从 t_order_item_logistics 表中获取信息
            OrderItemLogisticsDO orderItemLogisticsDO = orderItemLogisticsDOMapper.selectOne(new LambdaQueryWrapper<OrderItemLogisticsDO>()
                .eq(OrderItemLogisticsDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderItemLogisticsDO::getIsDeleted, false)
                .last(Constants.LIMIT_ONE));
            if (orderItemLogisticsDO != null){
                customerInfoVO.setRecipientPhone(orderItemLogisticsDO.getRecipientPhone());
                customerInfoVO.setRecipientPhoneMix(orderItemLogisticsDO.getRecipientPhoneMix());
                customerInfoVO.setRecipient(orderItemLogisticsDO.getRecipient());
                customerInfoVO.setRecipientAddress(orderItemLogisticsDO.getDetailAddress());
                customerInfoVO.setFullRecipientAddress(orderItemLogisticsDO.getFullDetailAddress());
            }
        }
        if (orderInfoDO.getBusinessCode().equals(BusinessIdEnum.VCS.getCode())){
            // 通过orderCode 从 t_vcs_order_info 表中获取信息
            VCSOrderInfoDO vcsOrderInfoDO = vcsOrderInfoDOService.getOne(new LambdaQueryWrapper<VCSOrderInfoDO>()
                .eq(VCSOrderInfoDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(VCSOrderInfoDO::getIsDeleted, false)
                .last(Constants.LIMIT_ONE));
            if (vcsOrderInfoDO != null){
                customerInfoVO.setIncontrolId(vcsOrderInfoDO.getIncontrolId());
                customerInfoVO.setIncontrolIdMix(vcsOrderInfoDO.getIncontrolIdMix());
                customerInfoVO.setConsumerCode(vcsOrderInfoDO.getConsumerCode());
            }
        }
        return customerInfoVO;
    }



    /**
     * 组装订单详细信息视图对象。
     *
     * @param orderInfoDO 订单信息
     * @return OrderDetailOrderInfoVO 订单详细信息视图对象
     */
    private OrderDetailOrderInfoVO assembleOrderInfoVO(OrderInfoDO orderInfoDO) {
        // 创建OrderDetailOrderInfoVO对象并设置属性
        OrderDetailOrderInfoVO orderInfoVO = new OrderDetailOrderInfoVO();

        // 商品履约类型
        orderInfoVO.setOrderType(orderInfoDO.getOrderType());

        //TODO 订单状态用于显示按钮
        //订单状态 枚举值和描述
        orderInfoVO.setOrderStatus(orderInfoDO.getOrderStatus());
        orderInfoVO.setOrderStatusDesc(OrderStatusEnum.getDescriptionByCode(orderInfoDO.getOrderStatus()));

        //支付、退款状态 枚举值和描述
        orderInfoVO.setPaymentStatus(orderInfoDO.getPaymentStatus());
        orderInfoVO.setPaymentStatusDesc(PaymentStatusEnum.getDescriptionByCode(orderInfoDO.getPaymentStatus()));
        orderInfoVO.setRefundStatus(orderInfoDO.getRefundStatus());
        orderInfoVO.setRefundStatusDesc(RefundStatusEnum.getDescriptionByCode(orderInfoDO.getRefundStatus()));

        //主订单号、子订单号
        orderInfoVO.setParentOrderCode(orderInfoDO.getParentOrderCode());
        orderInfoVO.setOrderCode(orderInfoDO.getOrderCode());

        //实付金额
        orderInfoVO.setCostAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getCostAmount())));

        // 通过 订单号的前两位获取品牌编码 LR or JA
        String brandCode = StringUtils.left(orderInfoDO.getOrderCode(), 2);
        orderInfoVO.setOrderChannelDesc(BrandCodeEnum.getBrandNameByBrandCode(brandCode));
        orderInfoVO.setOrderChannel(orderInfoDO.getOrderChannel());

        // 订单来源
        orderInfoVO.setOrderSource(OrderChannelCodeEnum.getOrderSourceByOrderChannelCode(orderInfoDO.getOrderChannel()));
        String orderCreator = "-";
        String receivePhone = "-";
        // 代客下单需要查询创建人
        if (OrderChannelCodeEnum.CUSTOMER_SERVICE.getOrderChannelCode().equals(orderInfoDO.getOrderChannel())) {
            CustomerServiceOrderDO customerServiceOrderDO = customerServiceOrderDOMapper.selectOne(new LambdaQueryWrapper<CustomerServiceOrderDO>()
                    .eq(CustomerServiceOrderDO::getOrderCode, orderInfoDO.getOrderCode())
                    .eq(CustomerServiceOrderDO::getIsDeleted, 0)
                    .orderByDesc(CustomerServiceOrderDO::getId)
                    .last(LIMIT_ONE));
            orderCreator = Optional.ofNullable(customerServiceOrderDO)
                    .map(CustomerServiceOrderDO::getCreateOperator)
                    .orElse("-");
            receivePhone = Optional.ofNullable(customerServiceOrderDO)
                    .map(CustomerServiceOrderDO::getRecievePhone)
                    .orElse("-");
        }
        orderInfoVO.setOrderCreator(orderCreator);

        if(!receivePhone.equals("-")){
            // 加密 代客下单 接受付款短信手机号 隐藏手机号中间4位
            orderInfoVO.setReceivePhoneMix((String) PhoneUtil.hideBetween(receivePhone));

            // 加密 代客下单 接受付款短信手机号 加密版
            CommonResult<String> commonResult1 = null;
            String encryptPhone;
            try {
                commonResult1 = permissionApi.getEncryptText(receivePhone);
            } catch (Exception e) {
                log.error("permissionApi加密失败", e);
            }
            if (commonResult1 != null && commonResult1.getData() != null) {
                encryptPhone = commonResult1.getData();
                orderInfoVO.setReceivePhone(encryptPhone);
            }
        }


        //客户留言、订单备注
        orderInfoVO.setCustomerRemark(orderInfoDO.getCustomerRemark());
        orderInfoVO.setOperatorRemark(orderInfoDO.getOperatorRemark());
        orderInfoVO.setOrderCloseReason(orderInfoDO.getOrderCloseReason());
        return orderInfoVO;
    }

    /**
     * 组装订单状态流转
     *
     * @param orderCode
     * @return
     */
    private OrderDetailOrderStatusVO assembleOrderStatusVO(String orderCode) {
        OrderDetailOrderStatusVO orderStatusVO = new OrderDetailOrderStatusVO();

        //查询订单状态log表
        List<OrderStatusLogDO> orderStatusLogDOList = orderStatusLogDOMapper.selectList(new LambdaQueryWrapperX<OrderStatusLogDO>()
                .eq(OrderStatusLogDO::getOrderCode, orderCode)
                .eq(OrderStatusLogDO::getIsDeleted, false)
                .orderByAsc(OrderStatusLogDO::getChangeTime));

        //准备orderStatusProgress容器
        List<OrderDetailOrderStatusVO.OrderStatusProgress> orderStatusProgressList = new ArrayList<>();

        for (OrderStatusLogDO orderStatusLogDO : orderStatusLogDOList) {
            OrderDetailOrderStatusVO.OrderStatusProgress orderStatusProgress = new OrderDetailOrderStatusVO.OrderStatusProgress();

            if (orderStatusLogDO != null) {
                orderStatusProgress.setAfterStatus(orderStatusLogDO.getAfterStatus());
                orderStatusProgress.setAfterStatusDesc(OrderStatusLogEnum.getDescriptionByCode(orderStatusLogDO.getAfterStatus()));
                orderStatusProgress.setChangeTime(TimeFormatUtil.timeToStringByFormat(orderStatusLogDO.getChangeTime(), TimeFormatUtil.formatter_6));
                if (OrderStatusEnum.AFTER_SALES.getCode().equals(orderStatusLogDO.getBeforeStatus())
                        && (OrderStatusEnum.COMPLETED.getCode().equals(orderStatusLogDO.getAfterStatus())
                        || OrderStatusEnum.ORDERED.getCode().equals(orderStatusLogDO.getAfterStatus()))
                ) {
                    orderStatusProgress.setChangeTime(TimeFormatUtil.timeToStringByFormat(orderStatusLogDO.getChangeTime(), TimeFormatUtil.formatter_6) + STATUS_TXT);
                }
            }

            orderStatusProgressList.add(orderStatusProgress);
        }
        orderStatusVO.setOrderStatusProgressList(orderStatusProgressList);

        return orderStatusVO;
    }

    /**
     * 根据订单号组装发票信息视图
     *
     * @param orderCode 订单号
     * @return OrderDetailInvoiceInfoVO 发票信息视图对象
     */
    private OrderDetailInvoiceInfoVO assembleInvoiceInfoVO(String orderCode) {
        // 1. 通过远程调用在payment服务中查询订单发票信息
        CommonResult<InvoiceDetailVO> invoiceResult = new CommonResult<>();
        OrderDetailInvoiceInfoVO invoiceInfoVO = new OrderDetailInvoiceInfoVO();

        try {
            invoiceResult = invoiceApi.getInvoiceDetail(orderCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (Objects.isNull(invoiceResult.getCode()) || invoiceResult.getCode() != 0 || Objects.isNull(invoiceResult.getData())) {
            return invoiceInfoVO;
        }

        InvoiceDetailVO invoiceDetailVO = invoiceResult.getData();
        // 2. 组装OrderDetailInvoiceInfoVO
        OrderDetailEInvoiceInfoVO eInvoiceInfoVO = new OrderDetailEInvoiceInfoVO();
        if (invoiceDetailVO.getEInvoiceVO() != null) {
            BeanUtils.copyProperties(invoiceDetailVO.getEInvoiceVO(), eInvoiceInfoVO);
        }
        invoiceInfoVO.setEInvoiceInfo(eInvoiceInfoVO);

        // 3. 组装OrderDetailPaperInvoiceInfoVO
        OrderDetailPaperInvoiceInfoVO paperInvoiceInfoVO = new OrderDetailPaperInvoiceInfoVO();
        if (invoiceDetailVO.getPaperInvoiceVO() != null) {
            BeanUtils.copyProperties(invoiceDetailVO.getPaperInvoiceVO(), paperInvoiceInfoVO);
        }
        invoiceInfoVO.setPaperInvoiceInfo(paperInvoiceInfoVO);

        // 4. 组装OrderDetailESpecialInvoiceInfoVO
        OrderDetailESpecialInvoiceInfoVO eSpecialInvoiceInfoVO = new OrderDetailESpecialInvoiceInfoVO();
        if (invoiceDetailVO.getESpecialInvoiceVO() != null) {
            BeanUtils.copyProperties(invoiceDetailVO.getESpecialInvoiceVO(), eSpecialInvoiceInfoVO);
        }
        invoiceInfoVO.setESpecialInvoiceInfo(eSpecialInvoiceInfoVO);
        // 返回组装好的视图对象
        return invoiceInfoVO;
    }

    /**
     * 根据业务线获取对应的履约方式编码
     */
    private String getFulfillmentCodeByBusinessName(boolean isVcsBusiness, OrderShopCarItemDTO item) {
        if (Objects.equals(item.getCartItemType(), CartItemTypeEnum.BRAND_GOODS.getCode())) {
            return OrderFulfillmentTypeEnum.BRAND_GOODS.getCode();
        }
        // VCS业务线且非实物商品返回VCS PIVI的code
        if (isVcsBusiness) {
            return OrderFulfillmentTypeEnum.VCS_PIVI.getCode();
        }
        throw new IllegalArgumentException("Invalid business name: " + item.getBusinessName());
    }

    /**
     * 检查订单中的购物车项是否全部属于同一业务线。
     *
     * @param orderCreateDTO 订单创建传输对象
     * @return 如果所有购物车项都有相同的业务线，则返回 true；否则返回 false。
     */
    private boolean isSingleBusiness(OrderCreateDTO orderCreateDTO) {
        // 使用 Stream API 从订单的购物车项中提取业务线。
        // 这里使用 map 方法将每个购物车项映射到其业务线。

        long distinctBusinessCount = orderCreateDTO.getShopCarItemList().stream()
                .map(OrderShopCarItemDTO::getBusinessName)
                // 使用 distinct 方法移除重复的业务线。
                .distinct()
                // 使用 count 方法计算不同业务线的数量。
                .count();

        // 如果不同业务线的数量大于 1，则表示有多种业务线，需要拆分。
        return distinctBusinessCount <= 1;
    }

    /**
     * 是否需要拆单
     */
    private boolean determineIfSplitNeeded(OrderCreateDTO orderCreateDTO) {
        // 第一层拆分逻辑：根据业务线拆分
        boolean splitByBusiness = !isSingleBusiness(orderCreateDTO);

        // 第二层拆分逻辑：VCS业务线按车辆拆分
        boolean splitByCar = orderCreateDTO.getShopCarItemList().stream()
                // 筛选businessName=VCS的
                .filter(item -> StringUtils.isNotBlank(item.getBusinessName()) &&
                        BusinessNameEnum.VCS.getName().equals(item.getBusinessName().toUpperCase()))
                .map(OrderShopCarItemDTO::getCarVin)
                .distinct()
                .count() > 1;

        // 第三层拆分逻辑：VCS业务线按商品履约方式进行拆分
        boolean splitByType = orderCreateDTO.getShopCarItemList().stream()
                // 筛选businessName=VCS的
                .filter(item -> StringUtils.isNotBlank(item.getBusinessName()) &&
                        BusinessNameEnum.VCS.getName().equals(item.getBusinessName().toUpperCase()))
                .map(OrderShopCarItemDTO::getCartItemType)
                .distinct()
                .count() > 1;

        return splitByBusiness || splitByCar || splitByType;
    }


    /**
     * 从OrderShopCarItemDTO构建OrderItemDO
     */
    private OrderItemDO buildOrderItem(OrderShopCarItemDTO itemDTO, String orderCode) {
        // 插库对象
        OrderItemDO orderItem = new OrderItemDO();

        orderItem.setOrderItemCode(String.valueOf(ecpIdUtil.nextId()));
        orderItem.setOrderCode(orderCode);

        orderItem.setProductVersionCode(itemDTO.getProductVersionCode());
        orderItem.setProductCode(itemDTO.getProductCode());
        orderItem.setProductSkuCode(itemDTO.getProductSkuCode());
        orderItem.setItemFulfillmentType(itemDTO.getCartItemType());
        orderItem.setProductName(itemDTO.getProductName());
        orderItem.setProductImageUrl(itemDTO.getProductImageUrl());

        orderItem.setProductAttribute(itemDTO.getProductAttribute());
        if (itemDTO.getMarketPrice() != null) {
            orderItem.setProductMarketPrice(MoneyUtil.convertToCents(itemDTO.getMarketPrice()).intValue());
        }
        orderItem.setProductSalePrice(MoneyUtil.convertToCents(itemDTO.getSalePrice()).intValue());
        orderItem.setProductQuantity(itemDTO.getQuantity());

        //应付总金额 = 商品销售价格 * 商品数量
        orderItem.setTotalAmount(MoneyUtil.convertToCents(itemDTO.getSalePrice()).multiply(BigDecimal.valueOf(itemDTO.getQuantity())).intValue());
        //实付总金额 = 实付价格 * 商品数量
        orderItem.setCostAmount(MoneyUtil.convertToCents(itemDTO.getPayPrice()).multiply(BigDecimal.valueOf(itemDTO.getQuantity())).intValue());
        //折扣金额 应付总金额 - 实付总金额
        orderItem.setDiscountFeeAmount(orderItem.getTotalAmount() - orderItem.getCostAmount());
        // 设置不含税总金额和税费总金额
        Integer excludeTaxAmount = MoneyUtil.calculateExcludeTaxAmount(itemDTO.getTaxRate(), orderItem.getCostAmount());
        Integer taxAmount = orderItem.getCostAmount() - excludeTaxAmount;
        orderItem.setExcludeTaxTotalAmount(excludeTaxAmount);
        orderItem.setTaxAmount(taxAmount);
        orderItem.setOrderItemSpuType(OrderItemSpuTypeEnum.getSpuType(itemDTO.getCartItemType()));
        return orderItem;
    }


    /**
     * 从DTO中提取信息并组装成OrderInfoDO对象
     *
     * @param orderCreateDTO  订单创建的DTO
     * @param item            用于订单信息组装的单个商品项
     * @param orderCode       订单号
     * @param parentOrderCode 父订单号
     * @return OrderInfoDO 包含订单信息的DO对象
     */
    private OrderInfoDO buildOrderInfo(OrderCreateDTO orderCreateDTO, OrderShopCarItemDTO item,
                                       String orderCode, String parentOrderCode, LocalDateTime now) {
        OrderInfoDO orderInfo = new OrderInfoDO();

        //代客下单时，如果订单备注不为空，则设置到操作员备注中
        if (Objects.nonNull(orderCreateDTO.getCustomerServiceOrderDTO()) &&
                StringUtils.isNotBlank(orderCreateDTO.getCustomerServiceOrderDTO().getCustomerServiceRemark()) &&
                "CS".equalsIgnoreCase(orderCreateDTO.getGlobalInfoDTO().getChannelCode())) {
            orderInfo.setOperatorRemark(orderCreateDTO.getCustomerServiceOrderDTO().getCustomerServiceRemark());
        }
        orderInfo.setConsumerCode(orderCreateDTO.getGlobalInfoDTO().getConsumerCode());
        orderInfo.setOrderCode(orderCode);

        orderInfo.setOriginalFeeTotalAmount(MoneyUtil.convertToCents(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getOriginalFeeTotalAmount()).intValue());
        orderInfo.setFeeTotalAmount(MoneyUtil.convertToCents(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getFeeTotalAmount()).intValue());
        orderInfo.setCostAmount(MoneyUtil.convertToCents(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getCostAmount()).intValue());
        orderInfo.setDiscountTotalAmount(MoneyUtil.convertToCents(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getDiscountTotalAmount()).intValue());
        orderInfo.setFreightAmount(MoneyUtil.convertToCents(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getFreightAmount()).intValue());
        orderInfo.setParentOrderCode(parentOrderCode != null ? parentOrderCode : orderCode);

        // 初始状态为"已下单"
        orderInfo.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        // 初始支付状态为"未支付"
        orderInfo.setPaymentStatus(PaymentStatusEnum.TO_BE_PAID.getCode());
        orderInfo.setOrderTime(now);
        orderInfo.setOrderChannel(OrderChannelCodeEnum.getOrderChannelCodeByChannelCode(orderCreateDTO.getGlobalInfoDTO().getChannelCode()));
        // 当cartItemType为0：虚拟组合商品时，orderType要设置为4
        orderInfo.setOrderType(item.getCartItemType().equals(CartItemTypeEnum.BUNDLED_GOODS.getCode())
                ? OrderTypeEnum.BUNDLED_GOODS.getCode() : item.getCartItemType());

        orderInfo.setWxNickName(orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getWxNickName());
        String wxPhone = orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getWxPhone();
        String contactPhone = orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getContactPhone();
        // 为代客下单时 t_order_info表不保存wx_phone、contact_phone
        String consumerCode = orderCreateDTO.getGlobalInfoDTO().getConsumerCode();
        boolean isCustomerServiceOrder = CUSTOMER_SERVICE_ORDER.equals(consumerCode);
        if(isCustomerServiceOrder) {
            wxPhone = null;
            contactPhone = null;
        }

        // 设置 phone
        orderInfo.setWxPhoneMix((String) PhoneUtil.hideBetween(wxPhone));
        orderInfo.setWxPhoneMd5(SecureUtil.md5(wxPhone));
        orderInfo.setContactPhoneMix((String) PhoneUtil.hideBetween(contactPhone));
        orderInfo.setContactPhoneMd5(SecureUtil.md5(contactPhone));
        // 加密手机号
        String encryptWxPhone = null;
        String encryptPhone = null;
        CommonResult<String> commonResult1 = null;
        CommonResult<String> commonResult2 = null;

        try {
            commonResult1 = permissionApi.getEncryptText(wxPhone);
            commonResult2 = permissionApi.getEncryptText(contactPhone);
        } catch (Exception e) {
            log.error("permissionApi解密失败", e);
        }

        if (commonResult1 != null && commonResult1.getData() != null) {
            encryptWxPhone = commonResult1.getData();
            orderInfo.setWxPhone(encryptWxPhone);
        }
        if (commonResult2 != null && commonResult2.getData() != null) {
            encryptPhone = commonResult2.getData();
            orderInfo.setContactPhone(encryptPhone);
        }


        orderInfo.setCustomerRemark(orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getOperatorRemark());

        orderInfo.setRefundStatus(RefundStatusEnum.NO_REFUND.getCode());
        orderInfo.setBusinessCode(item.getBusinessCode());
        // 赠品信息
        OrderGiftAddressDTO giftInfoDTO = orderCreateDTO.getGiftInfoDTO();
        orderInfo.setGiftAddress(giftInfoDTO.getNeedGift());

        orderInfo.setIndependentStatus(OrderIndependentStatusEnum.NO_NEED.getStatus());
        return orderInfo;
    }

    /**
     * 从OrderCreateDTO提取订单验证信息。
     *
     * @param orderCreateDTO 订单创建的DTO
     * @return OrderValidationInfo 包含订单验证所需的信息
     */
    private OrderValidationInfo extractOrderValidationInfo(OrderCreateDTO orderCreateDTO) {
        // 初始化订单验证信息对象
        OrderValidationInfo orderValidationInfo = new OrderValidationInfo();

        // 初始化购物车项列表
        List<CartItemDTO> cartItems = new ArrayList<>();

        // 初始化快速校验映射
        Map<String, CartItemDTO> cartItemMap = new HashMap<>();

        // 遍历OrderCreateDTO中的购物车项
        for (OrderShopCarItemDTO shopCarItem : orderCreateDTO.getShopCarItemList()) {

            CartItemDTO cartItem = new CartItemDTO();
            // 设置商品SPU编码
            cartItem.setProductSpuCode(shopCarItem.getProductCode());
            // 设置商品SKU编码
            cartItem.setProductSkuCode(shopCarItem.getProductSkuCode());
            // 设置销售价格
            cartItem.setSalePrice(shopCarItem.getSalePrice());
            // 设置实付价格：代客下单时这个字段 0<实付价格<=salePrice ；原有小程序端创建订接口，实付价格=salePrice
            cartItem.setPayPrice(shopCarItem.getPayPrice());
            // 设置商品数量
            cartItem.setQuantity(shopCarItem.getQuantity());

            cartItems.add(cartItem);

            // 添加到快速校验映射
            cartItemMap.put(shopCarItem.getProductSkuCode(), cartItem);
        }

        // 设置订单金额信息
        AllPaymentInfoDTO allPaymentInfoDTO = new AllPaymentInfoDTO();
        BeanUtils.copyProperties(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO(), allPaymentInfoDTO);
        orderValidationInfo.setPaymentInfoDTO(allPaymentInfoDTO);

        // 设置购物车项列表 和 映射
        orderValidationInfo.setCartItems(cartItems);
        orderValidationInfo.setCartItemMap(cartItemMap);

        return orderValidationInfo;
    }


    /**
     * 插入统计表
     *
     * @param consumerCode
     * @param vcsOrderCodes
     */
    /*public void insertStatistic(String consumerCode, List<String> vcsOrderCodes, String clientId) {
        OrderStatisticDO orderStatisticDO = orderStatisticDOMapper.selectOne(new LambdaQueryWrapperX<OrderStatisticDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderStatisticDO::getConsumerCode, consumerCode)
                .orderByDesc(OrderStatisticDO::getId)
                .last(Constants.LIMIT_ONE));
        if (orderStatisticDO == null) {
            orderStatisticDO = new OrderStatisticDO();
            orderStatisticDO.setConsumerCode(consumerCode);
            orderStatisticDO.setOrderTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.AGGREGATE_PARENT_ORDER.getFulfilmentType()));
            orderStatisticDO.setVcsOrderTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.VCS_PIVI.getFulfilmentType()));
            orderStatisticDO.setBrandGoodsTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.BRAND_GOODS.getFulfilmentType()));
            orderStatisticDOMapper.insert(orderStatisticDO);
        } else {
            orderStatisticDO.setOrderTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.AGGREGATE_PARENT_ORDER.getFulfilmentType()));
            orderStatisticDO.setVcsOrderTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.VCS_PIVI.getFulfilmentType()));
            orderStatisticDO.setBrandGoodsTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.BRAND_GOODS.getFulfilmentType()));
            orderStatisticDOMapper.updateById(orderStatisticDO);
        }
        //属于VCS商品
        if (CollUtil.isNotEmpty(vcsOrderCodes)) {
            List<VCSOrderInfoDO> vcsOrderInfoList = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                    .in(VCSOrderInfoDO::getVcsOrderCode, vcsOrderCodes)
                    .eq(BaseDO::getIsDeleted, false));
            if (CollUtil.isNotEmpty(vcsOrderInfoList)) {
                for (VCSOrderInfoDO vcsOrderInfoDO : vcsOrderInfoList) {
                    insertVcsStatistic(vcsOrderInfoDO, clientId);
                }
            }
        }
    }*/

    /**
     * 插入订单统计表
     *
     * @param consumerCode
     * @param vcsStatisticList
     * @param clientId
     */
    public void insertStatistic(String consumerCode, List<VCSOrderInfoDO> vcsStatisticList, String clientId) {
        OrderStatisticDO orderStatisticDO = orderStatisticDOMapper.selectOne(new LambdaQueryWrapperX<OrderStatisticDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderStatisticDO::getConsumerCode, consumerCode)
                .orderByDesc(OrderStatisticDO::getId)
                .last(Constants.LIMIT_ONE));
        if (orderStatisticDO == null) {
            orderStatisticDO = new OrderStatisticDO();
            orderStatisticDO.setConsumerCode(consumerCode);
            orderStatisticDO.setOrderTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.AGGREGATE_PARENT_ORDER.getFulfilmentType()));
            orderStatisticDO.setVcsOrderTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.VCS_PIVI.getFulfilmentType()));
            orderStatisticDO.setBrandGoodsTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.BRAND_GOODS.getFulfilmentType()));
            orderStatisticDOMapper.insert(orderStatisticDO);
        } else {
            orderStatisticDO.setOrderTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.AGGREGATE_PARENT_ORDER.getFulfilmentType()));
            orderStatisticDO.setVcsOrderTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.VCS_PIVI.getFulfilmentType()));
            orderStatisticDO.setBrandGoodsTotalCount(count(consumerCode, OrderFulfillmentTypeEnum.BRAND_GOODS.getFulfilmentType()));
            orderStatisticDOMapper.updateById(orderStatisticDO);
        }
        //属于VCS商品
        if (CollUtil.isNotEmpty(vcsStatisticList)) {
            for (VCSOrderInfoDO vcsOrderInfoDO : vcsStatisticList) {
                insertVcsStatistic(vcsOrderInfoDO, consumerCode, clientId);
            }
        }
    }

    /**
     * 插入vcs统计表
     *
     * @param vcsOrderInfoDO
     * @param consumerCode
     * @param clientId
     */
    private void insertVcsStatistic(VCSOrderInfoDO vcsOrderInfoDO, String consumerCode, String clientId) {
        VcsOrderStatisticDO vcsOrderStatisticDO = vcsOrderStatisticDOMapper.selectOne(new LambdaQueryWrapperX<VcsOrderStatisticDO>()
                .eq(VcsOrderStatisticDO::getConsumerCode, consumerCode)
                .eq(VcsOrderStatisticDO::getSeriesCode, vcsOrderInfoDO.getSeriesCode())
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(VcsOrderStatisticDO::getId)
                .last(Constants.LIMIT_ONE));
        if (vcsOrderStatisticDO == null) {
            vcsOrderStatisticDO = new VcsOrderStatisticDO();
            vcsOrderStatisticDO.setConsumerCode(consumerCode);
            vcsOrderStatisticDO.setSeriesCode(vcsOrderInfoDO.getSeriesCode());
            vcsOrderStatisticDO.setSeriesName(vcsOrderInfoDO.getSeriesName());
            vcsOrderStatisticDO.setOrderCount(vcsOrderInfoDO.getCount());
            //设置brandCode
            vcsOrderStatisticDO.setBrandCode(clientId);
            vcsOrderStatisticDOMapper.insert(vcsOrderStatisticDO);
        } else {
            vcsOrderStatisticDO.setOrderCount(vcsOrderInfoDO.getCount());
            vcsOrderStatisticDOMapper.updateById(vcsOrderStatisticDO);
        }
    }

    private void insertVcsStatistic(VCSOrderInfoDO vcsOrderInfoDO, String clientId) {
        //查询vcs统计
        Long l = vcsOrderInfoDOMapper.selectCount(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                .eq(VCSOrderInfoDO::getConsumerCode, vcsOrderInfoDO.getConsumerCode())
                .eq(VCSOrderInfoDO::getSeriesCode, vcsOrderInfoDO.getSeriesCode())
                .eq(BaseDO::getIsDeleted, false));

        VcsOrderStatisticDO vcsOrderStatisticDO = vcsOrderStatisticDOMapper.selectOne(new LambdaQueryWrapperX<VcsOrderStatisticDO>()
                .eq(VcsOrderStatisticDO::getConsumerCode, vcsOrderInfoDO.getConsumerCode())
                .eq(VcsOrderStatisticDO::getSeriesCode, vcsOrderInfoDO.getSeriesCode())
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(VcsOrderStatisticDO::getId)
                .last(Constants.LIMIT_ONE));
        if (vcsOrderStatisticDO == null) {
            vcsOrderStatisticDO = new VcsOrderStatisticDO();
            vcsOrderStatisticDO.setConsumerCode(vcsOrderInfoDO.getConsumerCode());
            vcsOrderStatisticDO.setSeriesCode(vcsOrderInfoDO.getSeriesCode());
            vcsOrderStatisticDO.setSeriesName(vcsOrderInfoDO.getSeriesName());
            vcsOrderStatisticDO.setOrderCount(l.intValue() == 0 ? 1 : l.intValue());
            //设置brandCode
            vcsOrderStatisticDO.setBrandCode(clientId);
            vcsOrderStatisticDOMapper.insert(vcsOrderStatisticDO);
        } else {
            vcsOrderStatisticDO.setOrderCount(l.intValue());
            vcsOrderStatisticDOMapper.updateById(vcsOrderStatisticDO);
        }
    }

    private Integer count(String consumerCode, Integer orderType) {
        LambdaQueryWrapper<OrderInfoDO> wrapper = new LambdaQueryWrapper<OrderInfoDO>()
                .eq(OrderInfoDO::getConsumerCode, consumerCode)
                .eq(BaseDO::getIsDeleted, false);
        if (OrderFulfillmentTypeEnum.AGGREGATE_PARENT_ORDER.getFulfilmentType().equals(orderType)) {
            wrapper.gt(OrderInfoDO::getOrderType, orderType);
        } else {
            wrapper.eq(OrderInfoDO::getOrderType, orderType);
        }


        Long count = orderInfoDOMapper.selectCount(wrapper);
        return count.intValue();
    }

    /**
     * 购物车参数校验
     *
     * @param shopCarItemList
     */
    private void validate(List<OrderShopCarItemDTO> shopCarItemList) {
        if (CollUtil.isEmpty(shopCarItemList)) {
            throw exception(SHOP_CAR_ITEM_EMPTY);
        }
        boolean match = shopCarItemList.stream().anyMatch(item -> item.getQuantity() <= 0);
        if (match) {
            throw exception(QUANTITY_INVALID);
        }
    }

    /**
     * 业务线校验
     */
    private void validateBusiness(OrderCreateDTO orderCreateDTO) {
        boolean hasNotVCS = orderCreateDTO.getShopCarItemList().stream()
                // 筛选businessName=VCS的
                .filter(item -> StringUtils.isBlank(item.getBusinessName()) ||
                        !BusinessNameEnum.VCS.getName().equals(item.getBusinessName().toUpperCase()))
                .map(OrderShopCarItemDTO::getCarVin)
                .distinct()
                .count() > 0;
        if (hasNotVCS) {
            throw exception(BUSINESS_INVALID);
        }
    }

    /**
     * 收货地址参数校验
     */
    private void validate(OrderGiftAddressDTO giftInfoDTO) {
        log.info("下单时的地址参数：{}", JSON.toJSONString(giftInfoDTO));

        // needGift为0或2时，不做校验
        if (!GiftAddressEnum.YES.getCode().equals(giftInfoDTO.getNeedGift())) {
            return;
        }

        // ======== 核心转换逻辑：adCode → 省市区编码 ========
        String adCode = giftInfoDTO.getAdCode();
        if (StrUtil.isNotBlank(adCode) && adCode.length() == 6) {
            // 如果 adCode 存在且格式正确，进行拆分
            String provinceCode = adCode.substring(0, 2);  // 取前2位（省级）
            String cityCode = adCode.substring(0, 4);      // 取前4位（省+市）
            String areaCode = adCode;                      // 完整adCode（区级）

            // 设置到DTO
            giftInfoDTO.setProvinceCode(provinceCode);
            giftInfoDTO.setCityCode(cityCode);
            giftInfoDTO.setAreaCode(areaCode);
        } else {
            // 如果 adCode 不存在或格式不正确，尝试使用现有的 provinceCode、cityCode 和 areaCode
            if (StrUtil.isBlank(giftInfoDTO.getProvinceCode()) ||
                    StrUtil.isBlank(giftInfoDTO.getCityCode()) ||
                    StrUtil.isBlank(giftInfoDTO.getAreaCode())) {
                log.info("adCode格式错误，且未提供有效的省市区编码");
                throw exception(GIFT_ADDRESS_INVALID, "adCode格式错误，且未提供有效的省市区编码");
            }
        }
        // ==================================================

        // 校验参数
        if (StrUtil.isBlank(giftInfoDTO.getProvinceCode()) || StrUtil.isBlank(giftInfoDTO.getCityCode()) || StrUtil.isBlank(giftInfoDTO.getAreaCode())) {
            throw exception(GIFT_ADDRESS_INVALID);
        }
        if (StrUtil.isBlank(giftInfoDTO.getDetailAddress()) || StrUtil.isBlank(giftInfoDTO.getRecipient()) || StrUtil.isBlank(giftInfoDTO.getRecipientPhone())) {
            throw exception(GIFT_ADDRESS_INVALID);
        }
    }

    /**
     * 组装APP赠品信息
     *
     * @param orderAppDetailPage 订单详情页面
     * @param orderInfoDO        订单信息
     */
    private void buildGiftInfoVO(OrderAppDetailPage orderAppDetailPage, OrderInfoDO orderInfoDO) {
        AppOrderGiftAddressDetailVO detailVO = new AppOrderGiftAddressDetailVO();
        // giftAddress为1
        if (GiftAddressEnum.YES.getCode().equals(orderInfoDO.getGiftAddress())) {
            detailVO = orderGiftAddressService.getOrderGiftAddressByOrderCode(orderInfoDO.getOrderCode());
            detailVO.setNeedGift(true);
        } else {
            detailVO.setNeedGift(false);
        }
        orderAppDetailPage.setGiftInfo(detailVO);
    }

    /**
     * 组装ADMIN赠品信息
     *
     * @param orderDetailRespVO 订单详情页面
     * @param orderInfoDO       订单信息
     */
    private void assembleGiftInfoVo(OrderInfoDO orderInfoDO, OrderDetailRespVO orderDetailRespVO) {
        OrderGiftAddressDetailVO detailVO = new OrderGiftAddressDetailVO();
        detailVO.setNeedGift(GiftAddressEnum.getDescription(orderInfoDO.getGiftAddress()));
        if (GiftAddressEnum.YES.getCode().equals(orderInfoDO.getGiftAddress())) {
            AppOrderGiftAddressDetailVO appDetailVO = orderGiftAddressService.getOrderGiftAddressByOrderCode(orderInfoDO.getOrderCode());
            // 返回mix信息和md5后的信息
            buildRecipientInfo(detailVO, appDetailVO);
        }
        orderDetailRespVO.setGiftInfo(detailVO);
    }

    /**
     * 构建收件人信息
     * 该方法主要用于处理收件人姓名和电话号码的混合和加密信息
     * 它接收两个参数对象，将处理后的信息存储在detailVO中
     *
     * @param detailVO    订单礼品地址详情对象，用于存储处理后的收件人信息
     * @param appDetailVO 应用订单礼品地址详情对象，包含原始收件人信息
     */
    private void buildRecipientInfo(OrderGiftAddressDetailVO detailVO, AppOrderGiftAddressDetailVO appDetailVO) {
        if (StrUtil.isNotBlank(appDetailVO.getRecipient())) {
            detailVO.setRecipientMix(maskChars(appDetailVO.getRecipient(), 1));
            detailVO.setRecipient(phoneNumberDecodeUtil.getEncryptText(appDetailVO.getRecipient()));
        }
        if (StrUtil.isNotBlank(appDetailVO.getRecipientPhone())) {
            detailVO.setRecipientPhoneMix((String) PhoneUtil.hideBetween(appDetailVO.getRecipientPhone()));
            detailVO.setRecipientPhone(phoneNumberDecodeUtil.getEncryptText(appDetailVO.getRecipientPhone()));
        }

        String province = Objects.isNull(appDetailVO.getProvince()) ? "" : appDetailVO.getProvince() + " ";
        String city = Objects.isNull(appDetailVO.getCity()) ? "" : appDetailVO.getCity() + " ";
        String area = Objects.isNull(appDetailVO.getArea()) ? "" : appDetailVO.getArea() + " ";
        String detailAddress = Objects.isNull(appDetailVO.getDetailAddress()) ? "" : appDetailVO.getDetailAddress();
        String address = province + city + area + detailAddress;

        if (StrUtil.isNotBlank(address)) {
            detailVO.setAddressMix(maskChars(address, 3));
            detailVO.setAddress(phoneNumberDecodeUtil.getEncryptText(address));
        }
    }

    /**
     * 将字符串的后 n 个字符设置为 *。
     *
     * @param input      输入的字符串
     * @param maskLength 掩码字符的数量
     * @return 处理后的字符串
     */
    public static String maskChars(String input, int maskLength) {
        if (input == null || input.length() <= maskLength) {
            return input; // 如果输入为空或长度小于等于 maskLength，直接返回输入
        }

        // 生成掩码字符串
        String mask = new String(new char[input.length() - maskLength]).replace("\0", "*");

        // 返回前 maskLength 个字符加上掩码
        return input.substring(0, maskLength) + mask;
    }

    /**
     * 检查是否存在在途的手动续费订单
     * 该方法通过调用manualRenewServiceApi接口来检查给定的车架号和服备类型组合是否存在在途订单
     *
     * @param carVinAndServiceTypeSet 一组包含车架号（VIN）和服务类型字符串的集合
     *                                每个字符串的格式应为"vin-serviceType"，以便于后续处理
     */
    public OrderCreateRespVO checkManualRenewInTransit(Set<String> carVinAndServiceTypeSet) {
        OrderCreateRespVO orderCreateRespVO = new OrderCreateRespVO();

        List<CheckRecordsInTransitDTO> reqDTOS = carVinAndServiceTypeSet.stream()
                .collect(Collectors.groupingBy(
                        carVinAndServiceType -> carVinAndServiceType.split(CONCAT_SYMBOL)[0],
                        Collectors.mapping(carVinAndServiceType -> Integer.parseInt(carVinAndServiceType.split(CONCAT_SYMBOL)[1]), Collectors.toList())
                ))
                .entrySet().stream()
                .map(entry -> {
                    CheckRecordsInTransitDTO dto = new CheckRecordsInTransitDTO();
                    dto.setCarVin(entry.getKey());
                    dto.setServiceTypeList(entry.getValue());
                    return dto;
                })
                .collect(Collectors.toList());

        log.info("校验是否存在在途的手动续费订单, reqDTOS:{}", reqDTOS);
        CommonResult<Boolean> result = manualRenewServiceApi.checkRecordsInTransit(reqDTOS);
        if (result == null || result.isError() || Boolean.TRUE.equals(result.getData())) {
            log.info("存在在途的手动续费订单, reqDTOS:{}", reqDTOS);
            orderCreateRespVO.setHasManualRenewalOrder(true);
            return orderCreateRespVO;
        }
        // 如果没有问题，则返回null，表示无需提前终止订单创建流程
        return null;
    }

    @Getter
    private static class InternalRefundInfo {
        private List<OrderRefundItemDO> orderRefundItemDOList;
        @Getter
        private BigDecimal hasRefundedAmount;
        private int hasRefundCount;
        private int refundingCount;
        private boolean foundCount;
        private List<OrderRefundDO> orderRefundDOList;
        private OrderRefundItemDO orderRefundItemDO;

        public InternalRefundInfo(List<OrderRefundItemDO> orderRefundItemDOList, BigDecimal hasRefundedAmount, int hasRefundCount, int refundingCount, boolean foundCount, List<OrderRefundDO> orderRefundDOList, OrderRefundItemDO orderRefundItemDO) {
            this.orderRefundItemDOList = orderRefundItemDOList;
            this.hasRefundedAmount = hasRefundedAmount;
            this.hasRefundCount = hasRefundCount;
            this.refundingCount = refundingCount;
            this.foundCount = foundCount;
            this.orderRefundDOList = orderRefundDOList;
            this.orderRefundItemDO = orderRefundItemDO;
        }

        public InternalRefundInfo invoke() {
            OrderRefundDO orderRefundDO = orderRefundDOList.stream().filter(x -> x.getRefundOrderCode().equals(orderRefundItemDO.getRefundOrderCode())).findFirst().orElse(null);
            if (orderRefundDO == null){
                log.info("refundOrderCode={}，找不到对应的退款订单信息",  orderRefundItemDO.getRefundOrderCode());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_INFO_LOST);
            }
            if(OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderRefundDO.getRefundFulfilmentType())) {
                if (List.of(FULL_REFUND_APPLY.getCode(), PARTIAL_REFUND_APPLY.getCode(), FULL_REFUND_APPROVE.getCode(),
                        PARTIAL_REFUND_APPROVE.getCode(), FULL_REFUND_COMPLETED.getCode(),
                        PARTIAL_REFUND_COMPLETED.getCode()).contains(orderRefundDO.getRefundOrderStatus())) {
                    hasRefundedAmount = hasRefundedAmount.add(BigDecimal.valueOf(orderRefundDO.getRefundMoneyAmount()));
                    int refundQuantity = orderRefundItemDOList.stream()
                            .mapToInt(OrderRefundItemDO::getRefundQuantity)
                            .sum(); // 如果流为空，返回 0
                    hasRefundCount = hasRefundCount + refundQuantity;
                    refundingCount = hasRefundCount;
                }
            }else if (OrderTypeEnum.BRAND_GOOD.getCode().equals(orderRefundDO.getRefundFulfilmentType())){//BG商品的状态需要审核通过才算做退款
                //如果是BG的，则只取第一条（因为已经按时间排了序），命中以后直接退出，BG只会
                int refundQuantity = orderRefundItemDO.getRefundQuantity();
                if (REFUND_COMPLETED.getCode() == orderRefundDO.getLogisticsRefundStatus()) {
                    if (!foundCount){//对于数量只取成功的第一条
                        hasRefundCount = hasRefundCount + refundQuantity;
                        foundCount = true;
                    }
                    hasRefundedAmount = hasRefundedAmount.add(BigDecimal.valueOf(orderRefundDO.getRefundMoneyAmount()));
                }
            }
            return this;
        }
    }
}






