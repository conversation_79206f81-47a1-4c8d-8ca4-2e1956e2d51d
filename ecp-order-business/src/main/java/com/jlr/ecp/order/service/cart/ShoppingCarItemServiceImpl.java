package com.jlr.ecp.order.service.cart;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.mybatis.core.query.QueryWrapperX;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.SkuStockRespDTO;
import com.jlr.ecp.inventory.dto.qry.QuerySkuStockDTO;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.cart.dto.CartGroupedBylineReq;
import com.jlr.ecp.order.controller.app.cart.vo.ShopCartMultiTypeProdVo;
import com.jlr.ecp.order.controller.app.cart.vo.ShoppingCarItemVO;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO;
import com.jlr.ecp.order.dal.mysql.cart.ShoppingCarItemMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CartItemTypeEnum;
import com.jlr.ecp.order.enums.cart.ProductShelfStatusEnum;
import com.jlr.ecp.order.enums.cart.ProductSkuStatusEnum;
import com.jlr.ecp.order.util.JacksonUtil;
import com.jlr.ecp.product.api.product.ProductApi;
import com.jlr.ecp.product.api.product.vo.ProductCartViewVO;
import com.jlr.ecp.product.api.product.vo.ProductCodeListVO;
import com.jlr.ecp.product.api.product.vo.ProductSkuRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * t_product_category(ProductCategory)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-07 20:13:51
 */
@Service("shoppingCarItemService")
@Validated
@Slf4j
public class ShoppingCarItemServiceImpl implements ShoppingCarItemService {

    @Resource
    private ShoppingCarItemMapper carItemMapper;

    @Resource
    private ProductApi productApi;

    @Resource
    private RedisService redisService;

    @Resource
    private InventoryOrderApi inventoryOrderApi;

    @Override
    public List<ShoppingCarItemVO> findCartList(String consumerCode, String brandCode, String incontrolName) {
        List<ShoppingCarItemDO> list = carItemMapper.selectList(new LambdaQueryWrapperX<ShoppingCarItemDO>()
                .eq(ShoppingCarItemDO::getConsumerCode, consumerCode)
                .eqIfPresent(ShoppingCarItemDO::getIncontrolId, incontrolName)
                .eqIfPresent(ShoppingCarItemDO::getBrandCode, brandCode)
                .eq(BaseDO::getIsDeleted, false));
        //查询商品集合详情
        List<String> productCodeList = list.stream()
                .map(ShoppingCarItemDO::getProductCode)
                .collect(Collectors.toList());

        //调用productAPI查询商品详情
        ProductCodeListVO productCodeListVO = new ProductCodeListVO();
        productCodeListVO.setProductCodeList(productCodeList);
        CommonResult<List<ProductCartViewVO>> result = productApi.getProductCartView(productCodeListVO);
        List<ProductCartViewVO> productCartViewList = result.getData();
        //一次性拉去redis数据
        Map<String, String> map = redisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY);
        Map<String, SeriesMappingVO> seriesMapping = new HashMap<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            seriesMapping.put(entry.getKey(), JSON.parseObject(entry.getValue(), SeriesMappingVO.class));
        }
        List<ShoppingCarItemVO> voList = new ArrayList<>();
        if (productCartViewList != null && !productCartViewList.isEmpty()) {
            //组装数据
            voList = list.stream().map(shoppingCarItemDO -> {
                ShoppingCarItemVO shoppingCarItemVO = new ShoppingCarItemVO();
                BeanUtils.copyProperties(shoppingCarItemDO, shoppingCarItemVO);
                //判断seriesMapping里面获取到的SeriesName是否为空，不为空取值seriesMapping里面的,否则原值
                shoppingCarItemVO.setSeriesName(seriesMapping.get(shoppingCarItemDO.getSeriesCode()) == null ? shoppingCarItemDO.getSeriesName() : seriesMapping.get(shoppingCarItemDO.getSeriesCode()).getSeriesName());
                //根据productCode拼装VO
                ProductCartViewVO productCartViewVO = productCartViewList.stream()
                        .filter(dataDO -> dataDO.getProductCode().equals(shoppingCarItemDO.getProductCode()))
                        .findFirst().orElse(new ProductCartViewVO());
                ShoppingCarServiceImpl.supplementShoppingCartItem(shoppingCarItemVO, productCartViewVO);
                return shoppingCarItemVO;
            }).collect(Collectors.toList());
        }

        return voList;
    }

    @Override
    public Integer countCartNum(String consumerCode, String incontrolName) {

        int shoppingCarItemCount = 0;
//
//        List<String> icrAccountList = new ArrayList<>();
//        if (StringUtils.isNotBlank(incontrolName)) {
//            icrAccountList = List.of(StringUtils.split(incontrolName, ","));
//        }
//
//        // 过滤掉 Empty String
//        icrAccountList = icrAccountList.stream().filter(
//                StringUtils::isNotBlank
//        ).collect(Collectors.toList());

        // 查询购物车项目
        QueryWrapperX<ShoppingCarItemDO> queryWrapperX = new QueryWrapperX<>();
        queryWrapperX.lambda().eq(ShoppingCarItemDO::getConsumerCode, consumerCode);
        queryWrapperX.lambda().eq(ShoppingCarItemDO::getIsDeleted, false);
        List<ShoppingCarItemDO> shoppingCarItemDoList = carItemMapper.selectList(queryWrapperX);

        if (ObjectUtil.isNotEmpty(shoppingCarItemDoList)) {

            // 调用 Product API 查询商品详情信息 (根据 SPU Code 查询)
            List<String> supCodeList = shoppingCarItemDoList.stream()
                    .map(ShoppingCarItemDO::getProductCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            ProductCodeListVO productCodeListVO = new ProductCodeListVO();
            productCodeListVO.setProductCodeList(supCodeList);
            CommonResult<List<ProductCartViewVO>> productCartViewListResult =
                    productApi.getProductCartView(productCodeListVO);

            List<ProductCartViewVO> productCartViewList = new ArrayList<>();
            if (null != productCartViewListResult && productCartViewListResult.getData() != null) {
                    productCartViewList = productCartViewListResult.getData();
            }

            List<ShoppingCarItemVO> shoppingCarItemVoList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(productCartViewList)) {

                // 组装数据
                List<ProductCartViewVO> finalProductCartViewList = productCartViewList;
                shoppingCarItemVoList = shoppingCarItemDoList.stream().map(shoppingCarItemDO -> {

                    ShoppingCarItemVO shoppingCarItemVO = new ShoppingCarItemVO();
                    BeanUtils.copyProperties(shoppingCarItemDO, shoppingCarItemVO);
                    // Set Business Code
                    ProductCartViewVO productCartViewVO = finalProductCartViewList.stream()
                            .filter(dataDO -> dataDO.getProductCode().equals(shoppingCarItemDO.getProductCode()))
                            .findFirst().orElse(new ProductCartViewVO());
                    shoppingCarItemVO.setBusinessCode(productCartViewVO.getBusinessCode());
                    return shoppingCarItemVO;
                }).collect(Collectors.toList());
            }

            shoppingCarItemCount = processShoppingCarItemForCount(shoppingCarItemVoList);
        }

        return shoppingCarItemCount;
    }

    /**
     * 处理 Shopping CarItem for Count
     *
     * @param shoppingCarItemVoList List of ShoppingCarItemVO
     * @return ItemCount
     */
    public Integer processShoppingCarItemForCount(List<ShoppingCarItemVO> shoppingCarItemVoList) {

        if (ObjectUtil.isNotEmpty(shoppingCarItemVoList)) {

            Map<String, List<ShoppingCarItemVO>> businessCodeCarItemMap = shoppingCarItemVoList.stream()
                    .collect(Collectors.groupingBy(
                            ShoppingCarItemVO::getBusinessCode,
                            Collectors.mapping(carItemDo -> carItemDo, Collectors.toList())
                    ));

            return businessCodeCarItemMap.values()
                    .stream()
                    .mapToInt(ItemVoList -> ItemVoList.stream().mapToInt(ShoppingCarItemVO::getQuantity).sum())
                    .sum();
        }

        return 0;
    }

    @Override
    public Boolean delete(List<String> cartItemCodes) {
        ShoppingCarItemDO shoppingCarItemDO = new ShoppingCarItemDO();
        shoppingCarItemDO.setIsDeleted(true);
        int update = carItemMapper.update(shoppingCarItemDO, new LambdaUpdateWrapper<ShoppingCarItemDO>()
                .in(ShoppingCarItemDO::getCartItemCode, cartItemCodes));
        return update > 0;
    }

    @Override
    public Boolean deleteByConsumerCode(String consumerCode) {
        ShoppingCarItemDO shoppingCarItemDO = new ShoppingCarItemDO();
        shoppingCarItemDO.setIsDeleted(true);
        try {
            carItemMapper.update(shoppingCarItemDO, new LambdaUpdateWrapper<ShoppingCarItemDO>()
                    .eq(ShoppingCarItemDO::getConsumerCode, consumerCode)
                    // 不为实物商品的都要删除
                    .ne(ShoppingCarItemDO::getCartItemType, CartItemTypeEnum.BRAND_GOODS.getCode()));
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CART_DELETE_FAIL);
        }
        return true;
    }

    @Override
    public Boolean deleteByConsumerCodeAndICR(String consumerCode, String incontrolId, String brandCode) {
        ShoppingCarItemDO shoppingCarItemDO = new ShoppingCarItemDO();
        shoppingCarItemDO.setIsDeleted(true);
        try {
            carItemMapper.update(shoppingCarItemDO, new LambdaUpdateWrapper<ShoppingCarItemDO>()
                    .eq(ShoppingCarItemDO::getConsumerCode, consumerCode)
                    .eq(ShoppingCarItemDO::getIncontrolId, incontrolId)
                    .eq(ShoppingCarItemDO::getBrandCode, brandCode)
                    // 不为实物商品的都要删除
                    .ne(ShoppingCarItemDO::getCartItemType, CartItemTypeEnum.BRAND_GOODS.getCode()));
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CART_DELETE_FAIL);
        }
        return true;
    }

    @Override
    public ShopCartMultiTypeProdVo productByServiceLineList(String jlrId, CartGroupedBylineReq cartGroupedBylineReq) {
//        List<String> icrAccountList;
//
//        icrAccountList = getIcrAccountList(cartGroupedBylineReq);

        // 在 DB 中根据用户 JLR ID 查询购物车 Item
        QueryWrapperX<ShoppingCarItemDO> queryWrapperX = new QueryWrapperX<>();
        queryWrapperX.lambda().eq(ShoppingCarItemDO::getConsumerCode, jlrId);
        queryWrapperX.lambda().eq(ShoppingCarItemDO::getIsDeleted, false);
        List<ShoppingCarItemDO> shoppingCarItemDOList = carItemMapper.selectList(queryWrapperX);

        log.info("在数据库查询到的购物车数据: {}", JSON.toJSONString(shoppingCarItemDOList));

        // 业务线-购物车 Item Map
        Map<String, List<ShoppingCarItemVO>> businessCodeCarItemMap = new LinkedHashMap<>();

        if (ObjectUtil.isNotEmpty(shoppingCarItemDOList)) {
            // 调用 Product API 查询商品详情信息 (根据 SPU Code 查询)
            List<String> supCodeList = shoppingCarItemDOList.stream()
                    .map(ShoppingCarItemDO::getProductCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("所有购物车商品的 SPU Code List: {}", JSON.toJSONString(supCodeList));

            ProductCodeListVO productCodeListVO = new ProductCodeListVO();
            productCodeListVO.setProductCodeList(supCodeList);
            productCodeListVO.setIncludeDownStatusSku(true);
            CommonResult<List<ProductCartViewVO>> productCartViewListResult =
                    productApi.getProductCartView(productCodeListVO);

            log.info("在 Product 查询到的数据 01: {}", JSON.toJSONString(productCartViewListResult));

            List<ProductCartViewVO> productCartViewList = new ArrayList<>();
            if (null != productCartViewListResult) {
                productCartViewList = productCartViewListResult.getData();
            }

            log.info("在 Product 查询到的数据 02: {}", JSON.toJSONString(productCartViewListResult));

            // 一次性拉取 Redis 数据
            Map<String, String> map = redisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY);
            Map<String, SeriesMappingVO> seriesMapping = new HashMap<>();
            Map<String, ProductCartViewVO> productCodeCartViewMap = getStringProductCartViewVOMap(map, seriesMapping, productCartViewList);

            List<ShoppingCarItemVO> shoppingCarItemVoList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(productCartViewList)) {

                for (ShoppingCarItemDO shoppingCarItemDO : shoppingCarItemDOList) {

                    ShoppingCarItemVO shoppingCarItemVO = new ShoppingCarItemVO();

                    // Copy Properties
                    BeanUtils.copyProperties(shoppingCarItemDO, shoppingCarItemVO);

                    // Set Series Name
                    setSeriesName(shoppingCarItemDO, seriesMapping, shoppingCarItemVO);

                    // Filter By Product Code
                    ProductCartViewVO productCartViewVO = productCodeCartViewMap.get(shoppingCarItemDO.getProductCode());

                    supplementCartItem(productCartViewVO, shoppingCarItemVO);

                    shoppingCarItemVoList.add(shoppingCarItemVO);
                }
            }

//            Set<String> icrAccountSet = new HashSet<>(icrAccountList);

//             如果有 ICR 账号则对 VCS 的数据进行过滤
//            shoppingCarItemVoList = getShoppingCarItemVOS(icrAccountList, shoppingCarItemVoList, icrAccountSet);

//            // 如果没有 ICR 账号则移除 VCS 内容
//            if (ObjectUtil.isEmpty(icrAccountList)) {
//                shoppingCarItemVoList = shoppingCarItemVoList.stream().filter(
//                        shoppingCarItemVO -> !BusinessIdEnum.VCS.getCode().equals(shoppingCarItemVO.getBusinessCode())
//                ).collect(Collectors.toList());
//            }

            // 将 shoppingCarItemDOList 按照业务线分组
            if (ObjectUtil.isNotEmpty(shoppingCarItemVoList)) {

                // 按照加入购物车的时间排序
                // Map<String, List<ShoppingCarItemVO>> businessCodeCarItemMap
                businessCodeCarItemMap = buildCustomBusinessCodeMap(shoppingCarItemVoList);

                // 处理商品失效逻辑
                processProductInvalidStatus(businessCodeCarItemMap);

                // 对 VCS 商品根据 ICR 账户以及品牌过滤
                List<ShoppingCarItemVO> shoppingCarItemVOList = businessCodeCarItemMap.get(BusinessIdEnum.VCS.getCode());

                filterShoppingCartItemVoList(shoppingCarItemVOList);

                log.info("原始查询到的 VCS 商品信息: {}", JSON.toJSONString(shoppingCarItemVOList));
            }
        }

        Map<String, List<ShoppingCarItemVO>> finalResultMap = new LinkedHashMap<>();

        addBusinessCodeCarItem(businessCodeCarItemMap, finalResultMap);

        // 处理业务线下所有商品都失效排序
        processBizLineAllInvalid(finalResultMap);

        // 设置 MIXED (BG + LRE) 下售罄标识
        setSoldOutVal(finalResultMap);

        // Build ShopCartMultiTypeProdVo
        ShopCartMultiTypeProdVo shopCartMultiTypeProdVo = new ShopCartMultiTypeProdVo();
        shopCartMultiTypeProdVo.setBusinessCodeCarItemMap(finalResultMap);

        return shopCartMultiTypeProdVo;
    }

    private LinkedHashMap<String, List<ShoppingCarItemVO>> buildCustomBusinessCodeMap(List<ShoppingCarItemVO> shoppingCarItemVoList) {
        return shoppingCarItemVoList.stream()
                .sorted(Comparator.comparing(ShoppingCarItemVO::getCreatedTime))
                .collect(Collectors.groupingBy(
                        it -> {
                            if (BusinessIdEnum.VCS.getCode().equals(it.getBusinessCode())) {
                                return it.getBusinessCode();
                            } else {
                                return Constants.MIXED;
                            }
                        },
                        LinkedHashMap::new,
                        Collectors.collectingAndThen(Collectors.toList(),
                                itemVoList -> {
                                    itemVoList.sort(Comparator.comparing(ShoppingCarItemVO::getCreatedTime));
                                    return itemVoList;
                                })));
    }

    private void setSoldOutVal(Map<String, List<ShoppingCarItemVO>> finalResultMap) {
        List<ShoppingCarItemVO> datas = finalResultMap.getOrDefault(Constants.MIXED, new ArrayList<>());
        // k-->金蝶 SKU, BG ； v -> Product详情
        Map<String, ProductSkuRespVO> skuInfoMap = datas.stream()
                .filter(itemVo -> StringUtils.equals(BusinessIdEnum.BRAND_GOODS.getCode(), itemVo.getBusinessCode()))
                .map(ShoppingCarItemVO::getSkuList)
                .filter(skuList -> !CollectionUtils.isEmpty(skuList))
                .flatMap(Collection::stream)
                .filter(it -> StringUtils.isNotBlank(it.getProductSkuCode()))
                .collect(Collectors.toMap(ProductSkuRespVO::getProductSkuCode, v -> v, (v1, v2) -> v1));
        Set<String> modelCodes = skuInfoMap.values().stream().map(ProductSkuRespVO::getModelCode)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 库存服务, sku字段, 会存 product sku 和 金蝶 sku 两种信息
        Map<String, Integer> modelCodeStockMap = new HashMap<>();
        if (CollectionUtils.isEmpty(modelCodes)) {
            log.info("[setSoldOutVal] request skus is empty");
        } else {
            log.info("[setSoldOutVal] 请求库存服务: {}", modelCodes);
            List<SkuStockRespDTO> skuStocks = Optional.ofNullable(inventoryOrderApi.querySkuStock(QuerySkuStockDTO.builder()
                            .skus(modelCodes).build()))
                    .map(CommonResult::getData).orElse(new ArrayList<>());
            log.info("[setSoldOutVal] 库存服务响应： {}", skuStocks);
            skuStocks.forEach(it -> {
                if (StringUtils.isNotBlank(it.getSku())) {
                    modelCodeStockMap.put(it.getSku(), Optional.ofNullable(it.getStock()).orElse(0));
                }
            });
            log.info("[setSoldOutVal] 查询到库存信息: {}", modelCodeStockMap);
        }
        for (ShoppingCarItemVO shoppingCarItemVO : datas) {
            //库存为0 --> 已售罄
            if (BusinessIdEnum.BRAND_GOODS.getCode().equals(shoppingCarItemVO.getBusinessCode())) {
                String modelCode = skuInfoMap.getOrDefault(shoppingCarItemVO.getProductSkuCode(), new ProductSkuRespVO()).getModelCode();
                if (StringUtils.isNotBlank(modelCode)) {
                    Integer stock = modelCodeStockMap.getOrDefault(modelCode, 0);
                    //MIXED中包含BG和LRE, 只有BG的才设置商品库存
                    shoppingCarItemVO.setSoldOut(stock <= 0);
                }
            } else {
                shoppingCarItemVO.setSoldOut(false);
            }
        }
    }

    private static List<ShoppingCarItemVO> getShoppingCarItemVOS(List<String> icrAccountList, List<ShoppingCarItemVO> shoppingCarItemVoList, Set<String> icrAccountSet) {
        if (ObjectUtil.isNotEmpty(icrAccountList)) {
            shoppingCarItemVoList = shoppingCarItemVoList.stream().filter(
                    shoppingCarItemVO -> {
                        // 仅对 VCS 内容进行过滤
                        if (BusinessIdEnum.VCS.getCode().equals(shoppingCarItemVO.getBusinessCode())) {
                            return icrAccountSet.contains(shoppingCarItemVO.getIncontrolId());
                        } else {
                            return true;
                        }
                    }
            ).collect(Collectors.toList());
        }
        return shoppingCarItemVoList;
    }

    @NotNull
    private static Map<String, ProductCartViewVO> getStringProductCartViewVOMap(Map<String, String> map, Map<String, SeriesMappingVO> seriesMapping, List<ProductCartViewVO> productCartViewList) {
        for (Map.Entry<String, String> entry : map.entrySet()) {
            seriesMapping.put(entry.getKey(), JSON.parseObject(entry.getValue(), SeriesMappingVO.class));
        }

        Map<String, ProductCartViewVO> productCodeCartViewMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(productCartViewList)) {
            productCodeCartViewMap = productCartViewList.stream().filter(Objects::nonNull).collect(
                    Collectors.toMap(ProductCartViewVO::getProductCode, viewVo -> viewVo)
            );
        }
        return productCodeCartViewMap;
    }

    private static void setSeriesName(ShoppingCarItemDO shoppingCarItemDO, Map<String, SeriesMappingVO> seriesMapping, ShoppingCarItemVO shoppingCarItemVO) {
        if (seriesMapping.get(shoppingCarItemDO.getSeriesCode()) == null) {
            shoppingCarItemVO.setSeriesName(shoppingCarItemDO.getSeriesName());
        } else {
            shoppingCarItemVO.setSeriesName(
                    seriesMapping.get(shoppingCarItemDO.getSeriesCode()).getSeriesName()
            );
        }
    }

    private static void addBusinessCodeCarItem(Map<String, List<ShoppingCarItemVO>> businessCodeCarItemMap, Map<String, List<ShoppingCarItemVO>> finalResultMap) {
        if (ObjectUtil.isNotEmpty(businessCodeCarItemMap)) {
            for (Map.Entry<String, List<ShoppingCarItemVO>> itemVoEntry : businessCodeCarItemMap.entrySet()) {
                String businessLineCode = itemVoEntry.getKey();

                // Values
                List<ShoppingCarItemVO> shoppingCarItemVoList = itemVoEntry.getValue();

                List<ShoppingCarItemVO> finalResultList = new ArrayList<>();

                addFinalResultList(shoppingCarItemVoList, finalResultList);

                finalResultMap.put(businessLineCode, finalResultList);
            }
        }
    }

    private static void supplementCartItem(ProductCartViewVO productCartViewVO, ShoppingCarItemVO shoppingCarItemVO) {
        if (null != productCartViewVO) {
            ShoppingCarServiceImpl.supplementShoppingCartItem(shoppingCarItemVO, productCartViewVO);
        }
    }

    private static void filterShoppingCartItemVoList(List<ShoppingCarItemVO> shoppingCarItemVOList) {
        if (ObjectUtil.isNotEmpty(shoppingCarItemVOList)) {
            // 针对 VCS 业务线需删除已被下架的 SKU 属性
            for (ShoppingCarItemVO shoppingCarItemVO : shoppingCarItemVOList) {
                List<ProductSkuRespVO> skuList = shoppingCarItemVO.getSkuList();

                // 过滤已下架的 SKU 属性
                List<ProductSkuRespVO> filterSkuList = skuList.stream()
                        .filter(Objects::nonNull)
                        .filter(sku -> ProductSkuStatusEnum.UP.getCode().equals(sku.getStatus()))
                        .collect(Collectors.toList());

                shoppingCarItemVO.setSkuList(filterSkuList);
            }
        }
    }

    private static void addFinalResultList(List<ShoppingCarItemVO> shoppingCarItemVoList, List<ShoppingCarItemVO> finalResultList) {
        if (ObjectUtil.isNotEmpty(shoppingCarItemVoList)) {

            // 失效商品 List
            List<ShoppingCarItemVO> failedGoods = new ArrayList<>();

            Iterator<ShoppingCarItemVO> iterator = shoppingCarItemVoList.iterator();
            while (iterator.hasNext()) {
                ShoppingCarItemVO shoppingCarItemVo = iterator.next();
                if (null != shoppingCarItemVo) {
                    Boolean valid = shoppingCarItemVo.getValid();

                    if (null == valid || !valid) {
                        failedGoods.add(shoppingCarItemVo);
                        iterator.remove();
                    }
                }
            }

            finalResultList.addAll(shoppingCarItemVoList);
            finalResultList.addAll(failedGoods);
        }
    }

    private static List<String> getIcrAccountList(CartGroupedBylineReq cartGroupedBylineReq) {
        List<String> icrAccountList;
        if (null != cartGroupedBylineReq) {
            icrAccountList = cartGroupedBylineReq.getIncontrolNameList();
        } else {
            icrAccountList = new ArrayList<>();
        }
        return icrAccountList;
    }

//    /**
//     * processShoppingCarItemVoList
//     *
//     * @param shoppingCarItemVoList List of ShoppingCarItemVO
//     * @param finalResultList       List of ShoppingCarItemVO
//     */
//    public void processShoppingCarItemVoList(
//            List<ShoppingCarItemVO> shoppingCarItemVoList,
//            List<ShoppingCarItemVO> finalResultList) {
//
//        addFinalResultList(shoppingCarItemVoList, finalResultList);
//    }

    /**
     * 当业务线下所有商品都失效时, 则处于排序的末尾
     *
     * @param finalResultMap Map of String List ShoppingCarItemVO
     */
    private void processBizLineAllInvalid(Map<String, List<ShoppingCarItemVO>> finalResultMap) {

        Map<String, List<ShoppingCarItemVO>> invalidMap = new LinkedHashMap<>();

        // 判断是否该业务线下所有商品都失效
        Iterator<Map.Entry<String, List<ShoppingCarItemVO>>> iterator = finalResultMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<ShoppingCarItemVO>> next = iterator.next();

            String bizLineCode = next.getKey();
            List<ShoppingCarItemVO> value = next.getValue();

            if (ObjectUtil.isNotEmpty(value)) {
                Set<Boolean> allInvaildSet = value.stream()
                        .map(ShoppingCarItemVO::getValid)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                if (allInvaildSet.size() == 1 && allInvaildSet.contains(false)) {
                    invalidMap.put(bizLineCode, value);
                    iterator.remove();
                }
            }
        }

        finalResultMap.putAll(invalidMap);

    }

    /**
     * 处理商品失效逻辑
     *
     * @param businessCodeCarItemMap Map of BusinessLine and List ShoppingCarItemVo
     */
    private void processProductInvalidStatus(Map<String, List<ShoppingCarItemVO>> businessCodeCarItemMap) {

        for (Map.Entry<String, List<ShoppingCarItemVO>> businessLineEntry : businessCodeCarItemMap.entrySet()) {
            String businessLineCode = businessLineEntry.getKey();

            // 如果是 VCS 业务线则单独处理
            if (BusinessIdEnum.VCS.getCode().equals(businessLineCode)) {
                List<ShoppingCarItemVO> vcsProductCarVo = businessLineEntry.getValue();

                if (ObjectUtil.isNotEmpty(vcsProductCarVo)) {
                    processVcsProductInvalidStatus(businessLineEntry);
                }
            } else {
                // 处理其它业务线失效逻辑
                List<ShoppingCarItemVO> bizLineShoppingCarItemList = businessLineEntry.getValue();
                if (ObjectUtil.isNotEmpty(bizLineShoppingCarItemList)) {
                    processNonVcsProductInvalidStatus(bizLineShoppingCarItemList);
                }
            }
        }
    }

    public void processVcsProductInvalidStatus(
            Map.Entry<String, List<ShoppingCarItemVO>> businessLineEntry) {

        List<ShoppingCarItemVO> vcsProductCarVo = businessLineEntry.getValue();

        if (ObjectUtil.isNotEmpty(vcsProductCarVo)) {
            for (ShoppingCarItemVO shoppingCarItemVO : vcsProductCarVo) {
                if (null != shoppingCarItemVO) {
                    Integer shelfStatus = shoppingCarItemVO.getShelfStatus();

                    // 初始化商品状态为 TRUE
                    shoppingCarItemVO.setValid(Boolean.TRUE);

                    // 如果商品状态不为上架则有效性为 FALSE
                    if (!ProductShelfStatusEnum.UP.getCode().equals(shelfStatus)) {
                        shoppingCarItemVO.setValid(Boolean.FALSE);
                    }
                }
            }
        }
    }

    /**
     * 处理其他业务线失效逻辑
     *
     * @param bizLineShoppingCarItemList List of ShoppingCarItemVO
     */
    public void processNonVcsProductInvalidStatus(
            List<ShoppingCarItemVO> bizLineShoppingCarItemList) {

        bizLineShoppingCarItemList = bizLineShoppingCarItemList.stream()
                .filter(Objects::nonNull).collect(Collectors.toList());

        for (ShoppingCarItemVO shoppingCarItemVO : bizLineShoppingCarItemList) {

            // 初始化商品有效性为 TRUE
            shoppingCarItemVO.setValid(Boolean.TRUE);
            String productSkuCode = shoppingCarItemVO.getProductSkuCode();
            List<ProductSkuRespVO> skuList = shoppingCarItemVO.getSkuList();
            if (ObjectUtil.isNotEmpty(skuList)) {

                // 如果商品未处于上架状态则直接判断为失效状态
                Integer shelfStatus = shoppingCarItemVO.getShelfStatus();

                // 如果商品状态不为上架状态则有效性为 FALSE
                if (!ProductShelfStatusEnum.UP.getCode().equals(shelfStatus)) {
                    shoppingCarItemVO.setValid(Boolean.FALSE);
                } else {
                    List<ProductSkuRespVO> filterSkuList = skuList.stream()
                            .filter(Objects::nonNull).filter(sku -> sku.getProductSkuCode().equals(productSkuCode))
                            .collect(Collectors.toList());

                    // Assert Sku List Size
                    //assertFilterSkuListSize(filterSkuList);

                    //如果filterSkuList 为空了，说明没找到，这时候我们认为是无效的商品
                    if (CollUtil.isEmpty(filterSkuList) || (null != filterSkuList.get(0) && ProductSkuStatusEnum.DOWN.getCode().equals(filterSkuList.get(0).getStatus()))) {
                        shoppingCarItemVO.setValid(Boolean.FALSE);
                    }
                }
            }
        }
    }
}

