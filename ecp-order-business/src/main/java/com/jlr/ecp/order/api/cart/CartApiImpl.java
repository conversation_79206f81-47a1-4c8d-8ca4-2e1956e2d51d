package com.jlr.ecp.order.api.cart;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.service.cart.ShoppingCarItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 购物车API实现
 */
@RestController
@Validated
@Slf4j
public class CartApiImpl implements CartApi {

    @Resource
    private ShoppingCarItemService shoppingCarItemService;

    @Override
    public CommonResult<String> deleteByConsumerCode(String consumerCode) {
        if(shoppingCarItemService.deleteByConsumerCode(consumerCode)){
            return CommonResult.success(Constants.CART_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.CART_DELETE_FAIL);
    }

    @Override
    public CommonResult<String> deleteByConsumerCodeAndICR(String consumerCode, String incontrolId, String brandCode) {
        if(shoppingCarItemService.deleteByConsumerCodeAndICR(consumerCode, incontrolId, brandCode)){
            return CommonResult.success(Constants.CART_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.CART_DELETE_FAIL);
    }
}
